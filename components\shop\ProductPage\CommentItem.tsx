import { Info, ThumbsUp, ThumbsDown, CircleUserRound } from 'lucide-react'
import ProfilePic from "@/public/assets/images/profile-pic.png"
import Image from 'next/image'
import { ProductComment } from '@/lib/types/product.types'
import RatingStars from './RattingStars'
const CommentItem = ({comment}: {comment: ProductComment}) => {
    return (
        <div className='my-5'>
            <div className="bg-[#F5F6F8] rounded-2xl min-h-52 pb-5">
                <div className="commets-header flex justify-between md:w-[88%] max-md:w-full mx-auto pt-10 max-md:px-2">
                    <div className="right flex items-center md:gap-5 max-md:gap-2 md:w-[45%]">
                        <div className='max-w-[20%]'>
                            {/* <Image src={ProfilePic} alt="profile-pic" /> */}
                            <CircleUserRound size={40} />
                        </div>
                        <div className="flex flex-col">
                            <h3>
                                {comment.user.full_name}
                            </h3>
                            <div className="flex items-center text-sm max-md:text-xs h-7 gap-2">
                                <Info size={18} />
                                <span className="">
                                   {comment.created_at}
                                </span>
                                {/* <span>
                                    
                                </span> */}
                            </div>
                        </div>
                    </div>
                    <div className="left flex items-center gap-5 bg-gradient-to-l from-white to-transparent md:p-2.5 max-md: rounded-3xl">
                        <div className="flex items-center gap-5 border md:px-3 py-2 max-md:p-2 max-md:py-3 rounded-3xl text-gray-400">
                            <p className="flex items-center text-sm md:gap-2">
                                <span>
                                    0
                                </span>
                                <span>
                                    <ThumbsUp size={24} className=' max-md:w-5' />
                                </span>
                            </p>
                            <p className="flex items-center text-sm gap-2">
                                <span>
                                    0
                                </span>
                                <span>
                                    <ThumbsDown size={24} className=' max-md:w-5' />
                                </span>
                            </p>

                        </div>
                        <div className='review flex gap-2 items-center h-full my-2 max-md:hidden'>
                            <span className='text-sm'> {comment.rate} </span>
                            {/* <div className='flex gap-1 items-center h-full mr-2 max-md:hidden'>
                                <Star fill='#9DA5B0' className='w-4 text-[#9DA5B0]' />
                                <Star fill='#9DA5B0' className='w-4 text-[#9DA5B0]' />
                                <Star fill='#9DA5B0' className='w-4 text-[#9DA5B0]' />

                                <Star className='w-4 text-[#F7BC06]' fill='#F7BC06' /> <Star className='w-4 text-[#F7BC06]' fill='#F7BC06' />

                            </div> */}
                            <RatingStars rate={comment.rate} />

                        </div>

                    </div>

                </div>
                <div className="comments-body md:px-5 max-md:px-3 py-5 mb-2 mt-7 w-[94%] mx-auto bg-white rounded-3xl ">
                    <div className="mb-7">
                        <p className="leading-8 max-md:text-sm max-md:leading-7">
                                {comment.body}
                        </p>
                    </div>
                    {/* <div className="max-w-20 h-16 flex gap-4">
                        <Image className="rounded-xl" src={Motoroil} alt="product-img" />
                        <Image className="rounded-xl" src={Motoroil} alt="product-img" />
                        <Image className="rounded-xl" src={Motoroil} alt="product-img" />
                    </div> */}

                </div>

            </div>
        </div>
    )
}

export default CommentItem