import React, { useState, useRef, useEffect } from "react";
import { ChevronDown } from "lucide-react";

type PlateInputProps = {
  value: {
    input1: string;
    input2: string;
    input3: string;
    alphabet: string;
  };
  onChange: (val: {
    input1: string;
    input2: string;
    input3: string;
    alphabet: string;
  }) => void;
  input1Ref: React.RefObject<HTMLInputElement>;
  input2Ref: React.RefObject<HTMLInputElement>;
  input3Ref: React.RefObject<HTMLInputElement>;
  readOnly?: boolean;
};

const plateLetters = [
  "الف","ب","پ","ت","ث","ج","چ","ح","خ",
  "د","ذ","ر","ز","س","ش","ص","ض","ط","ظ",
  "ع","غ","ف","ق","ک","گ","ل","م","ن","و","ه","ی",
  "معلولین","تشریفات"
];

export default function PlateInput({
  value,
  onChange,
  input1Ref,
  input2Ref,
  input3Ref,
  readOnly = false,
}: PlateInputProps) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const alphabetRef = useRef<HTMLDivElement>(null);

  // بستن دراپ‌داون روی کلیک خارج
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (alphabetRef.current && !alphabetRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // تغییر مقدار input
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: "input1" | "input2" | "input3"
  ) => {
    const newVal = e.target.value.replace(/\D/g, "").slice(0, 2);
    onChange({ ...value, [field]: newVal });

    if (newVal.length === 2) {
      if (field === "input1") input2Ref.current?.focus();
      if (field === "input2") input3Ref.current?.focus();
    }
  };

  // هندل بک‌اسپیس
  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    field: "input2" | "input3"
  ) => {
    if (e.key === "Backspace" && value[field].length === 0) {
      if (field === "input2") input1Ref.current?.focus();
      if (field === "input3") input2Ref.current?.focus();
    }
  };

  return (
    <div className="flex items-center gap-2 border rounded-lg p-2 bg-white">
      {/* بخش راست */}
      <input
        ref={input1Ref}
        type="text"
        maxLength={2}
        value={value.input1}
        onChange={(e) => handleInputChange(e, "input1")}
        readOnly={readOnly}
        className="w-10 text-center border rounded p-1"
      />

      {/* بخش حرف */}
      <div ref={alphabetRef} className="relative">
        <div
          className="w-12 h-10 flex items-center justify-center border rounded cursor-pointer bg-gray-50"
          onClick={() => !readOnly && setDropdownOpen((prev) => !prev)}
        >
          {value.alphabet || "حرف"}
          {!readOnly && <ChevronDown className="w-4 h-4 ml-1" />}
        </div>
        {dropdownOpen && (
          <div className="absolute top-12 right-0 bg-white border rounded shadow-lg max-h-40 overflow-y-auto z-10 w-28">
            {plateLetters.map((letter) => (
              <div
                key={letter}
                className="px-2 py-1 hover:bg-gray-100 cursor-pointer text-center"
                onClick={() => {
                  onChange({ ...value, alphabet: letter });
                  setDropdownOpen(false);
                  input2Ref.current?.focus();
                }}
              >
                {letter}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* بخش وسط */}
      <input
        ref={input2Ref}
        type="text"
        maxLength={3}
        value={value.input2}
        onChange={(e) => handleInputChange(e, "input2")}
        onKeyDown={(e) => handleKeyDown(e, "input2")}
        readOnly={readOnly}
        className="w-12 text-center border rounded p-1"
      />

      {/* بخش چپ */}
      <input
        ref={input3Ref}
        type="text"
        maxLength={2}
        value={value.input3}
        onChange={(e) => handleInputChange(e, "input3")}
        onKeyDown={(e) => handleKeyDown(e, "input3")}
        readOnly={readOnly}
        className="w-10 text-center border rounded p-1"
      />
    </div>
  );
}
