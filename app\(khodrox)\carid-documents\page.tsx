import InquiryComponent from '@/components/inquiry/InquiryComponent'
import Faq from '@/components/InquiryStaticComponents/Faq'
import envConfig from "@/lib/config-env";
import ChildSchema from '@/components/common/ChildSchema';
import { PageContentResponse } from '@/lib/types/types';
import ArticleSection from '@/components/InquiryStaticComponents/ArticleSection';
import { getPageContent } from '@/lib/utils';
import CarIdDocumentsFormWrapper from '@/components/CarIdDocuments/CarIdDocumentsFormWrapper';


export async function generateMetadata() {
    const data = await getPageContent("carid-documents");

    return {
        title: data.meta_title,
        description: data.meta_description,
    };
}



export const revalidate = 0

const isMotor: boolean = false
const withDetails: boolean | undefined = true


const CarIdDocomentsPage = async () => {
    const env = envConfig()
    const status = env.Services.VEHICLE_CARD_AND_DOCUMENT_STATUS_SECTION
    const data: PageContentResponse = await getPageContent("carid-documents")
    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id='carid-documents'
                    schema={schema}
                />
            }
            <CarIdDocumentsFormWrapper
                title={title || ""}
                isMotor={isMotor}
                status={status}
            />
            
            {
                description &&
                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
        </>
    )
}

export default CarIdDocomentsPage