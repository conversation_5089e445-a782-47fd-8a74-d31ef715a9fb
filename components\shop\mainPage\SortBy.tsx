"use client";
import { Dispatch, SetStateAction, useState } from "react";
import { ArrowDownNarrowWide, LayoutGrid, SlidersHorizontal, Trash2 } from "lucide-react";
import { ProductFilterOptions, SortType } from "@/lib/types/product.types";
import ProductFilterMobileDrawer from "@/components/shop/ProductFilterMobileDrawer";
import SortFilter from "@/components/shop/mainPage/SortFilter";

type Props = {
    onFilterClick: () => void;
    onCategoryClick?: () => void;
    selectedSort?: SortType;
    setProductParams?: Dispatch<SetStateAction<ProductFilterOptions>>
    totalCount?: number
    onResetFilters?: () => void;
}

const SortingFilter = ({
    onFilterClick,
    onResetFilters,
    setProductParams,
    onCategoryClick,
    selectedSort,
    totalCount = 0
}: Props) => {
    const [openSortDrawer, setOpenSortDrawer] = useState(false)


    const sortingOptions: { label: string; value: SortType }[] = [
        {
            value: "newest",
            label: "جدیدترین",
        },
        {
            value: "cheapest",
            label: "ارزان ترین",
        },
        {
            value: "most_sales",
            label: "پرفروش ترین",
        },
        {
            value: "most_expensive",
            label: "گران ترین",
        },
        {
            value: "most_viewed",
            label: "پر بازدید ترین",
        },
        {
            value: "most_popular",
            label: "محبوب ترین",
        },
    ]

    const selectedSortName = sortingOptions.find(option => option.value === selectedSort)?.label

    function handleSortChange(value: SortType) {
        setProductParams?.((prevState) => ({ ...prevState, sort: value, page: 1 }));
    }

    return (
        <>
            <ProductFilterMobileDrawer
                isOpen={openSortDrawer}
                onClose={() => setOpenSortDrawer(false)}
                showCloseBtn={false}
                halfScreen={true}
            >
                <SortFilter
                    onSortSelect={handleSortChange}
                    onClose={() => setOpenSortDrawer(false)}
                    sortingOptions={sortingOptions}
                    selectedValue={selectedSort}
                />
            </ProductFilterMobileDrawer>
            <div
                className="max-md:sticky md:block max-md:top-0
             max-md:px-3 max-md:w-full
              max-md:border-b mx-md:border-gray-400 max-md:left-0
               max-md:right-0 max-md:z-10 max-md:bg-white md:mt-6 max-w-7xl mx-auto">
                <div
                    className="flex max-sm:gap-2 max-lg:justify-start md:justify-between md:gap-8 items-center text-sm w-full max-md:w-full md:px-0 mx-auto h-12 md:h-20 max-md:px-0 max-md:text-sm">
                    {/* Filters Section */}

                    <div
                        className="md:w-[32%] lg:w-[24%] h-full bg-white flex justify-center md:justify-between items-center gap-6 md:px-5 max-md:text-sm rounded-3xl px-1 max-md:hidden">
                        <span className="flex flex-row-reverse gap-2 items-center ">فیلترها
                            <ArrowDownNarrowWide className="md:hidden" />
                        </span>

                        <button
                            onClick={() => onResetFilters?.()}
                            className="bg-[#FF4A4A] max-md:hidden max-md:mx-1 text-white px-4 py-2 rounded-3xl flex flex-row-reverse gap-2 whitespace-nowrap text-sm items-center">
                            پاک کردن <Trash2 className="w-5" />
                        </button>

                    </div>
                    <div
                        onClick={() => onFilterClick?.()}
                        className="md:col-span-2  max-md:py-1 bg-white flex justify-center md:justify-between items-center gap-6 max-md:text-sm rounded-3xl px-1 md:hidden cursor-pointer">
                        <span className="flex flex-row-reverse gap-x-[4px] items-center max-md:text-xs">
                            فیلترها
                            <ArrowDownNarrowWide className="md:hidden size-5" />
                        </span>
                        {/*<span*/}
                        {/*    className={`bg-gradient-to-t from-gray-100 to-transparent md:hidden rounded-full p-1 transition-transform duration-200 `}>*/}
                        {/*    <ChevronDown/>*/}
                        {/*</span>*/}
                    </div>

                    <div onClick={() => onCategoryClick?.()}
                        className="md:col-span-2   max-md:px-2 max-md:py-1 max-md:mx-1 bg-white flex justify-center md:justify-between items-center gap-6 max-md:text-sm rounded-3xl px-3 md:hidden cursor-pointer">
                        <span
                            className="flex flex-row-reverse gap-2 items-center whitespace-nowrap max-md:text-xs">دسته بندی
                            <LayoutGrid className="md:hidden size-4" />
                        </span>
                        {/*<span*/}
                        {/*    className={`bg-gradient-to-t from-gray-100 to-transparent md:hidden rounded-full p-1 transition-transform duration-200 `}>*/}
                        {/*    <ChevronDown/>*/}
                        {/*</span>*/}
                    </div>


                    {/* Sorting Section */}
                    <div
                        className="bg-white flex-1 max-md:max-w-fit h-full w-full md:col-span-9 gap-3 flex items-center px-1 rounded-3xl relative">
                        {/* Desktop Sorting */}
                        <div className="hidden md:flex gap-1 text-black  items-center text-nowrap text-xs font-bold">
                            <SlidersHorizontal className="size-4" />
                            مرتب سازی:
                        </div>

                        {/* Desktop Sorting List */}
                        <ul className="hidden md:flex flex-wrap gap-0 lg:gap-x-1 text-sm">
                            {sortingOptions.map(({ value, label }, index) => (
                                <li
                                    key={index}
                                    className={`cursor-pointer text-[#5E646B] py-2 px-3 text-nowrap text-xs !font-light rounded-3xl ${selectedSort === value ? "bg-primary text-white" : ""}`}
                                    onClick={() => handleSortChange(value)}
                                >
                                    {label}
                                </li>
                            ))}
                        </ul>

                        <div className="md:hidden max-w-fit relative w-full">
                            <button
                                className="w-full  max-md:py-1 md:px-2 py-2 rounded-3xl gap-x-[4px] flex justify-between items-center text-sm"
                                onClick={(e) => {
                                    setOpenSortDrawer(true)
                                }}
                            >
                                <SlidersHorizontal className='size-4' />
                                <span
                                    className='max-md:text-xs whitespace-nowrap'>{selectedSortName || "مرتب سازی"}</span>
                                {/*    <span*/}
                                {/*        className={`bg-gradient-to-t from-gray-100 to-transparent rounded-full p-1 transition-transform duration-200 ${dropdownOpen ? "rotate-180" : "rotate-0"}`}>*/}
                                {/*    /!*<ChevronDown/>*!/*/}
                                {/*</span>*/}
                            </button>
                        </div>

                        {totalCount > 0 && <div
                            className="bg-gradient-to-l max-lg:hidden absolute top-[50%] transform translate-y-[-50%] w-[150px] left-0 from-gray-100 to-transparent
                         mr-20 py-3 rounded-3xl">
                            <div className='flex justify-center items-center w-full'>
                                <span className="text-gray-800 text-sm">{`(${totalCount})`}</span>
                                <span className='text-[#9DA5B0] text-sm mr-1'>محصول</span>
                            </div>
                        </div>}
                    </div>
                </div>
            </div>
        </>
    );
};

export default SortingFilter;
