"use client"
import EmptyCover from "@/public/assets/images/empty-cover.webp"
import Image from 'next/image'
import { Eye, TrashIcon } from 'lucide-react'
import BasketCartIcon from '@/components/common/svg/BasketCartIcon'
import { FavoriteItem } from "@/lib/types/favorites.types"
import { useRouter } from "nextjs-toploader/app"
import { deleteFromFavorites } from "@/actions/favorites.action"
import toast from "react-hot-toast"
import { useState } from "react"

interface FavoritesPageItemProps {
    product: FavoriteItem
    setFavoriteProducts: React.Dispatch<React.SetStateAction<FavoriteItem[]>>

}

const FavoritesPageItem = ({ product, setFavoriteProducts }: FavoritesPageItemProps) => {
    const [isDeleting, setIsDeleting] = useState(false)
    const router = useRouter()



    const handleDeleteFromFavorites = async () => {
        setIsDeleting(true)
        const deleteResponse = await deleteFromFavorites(product.slug)
        if (deleteResponse.success) {
            toast.success("محصول با موفقیت از لیست علاقه مندی ها حذف شد")
            setFavoriteProducts((prevProducts) => prevProducts.filter((p) => p.slug !== product.slug))
        } else {
            toast.error("خطایی رخ داده")
        }
        setIsDeleting(false)

    }
    return (
        <div className='w-[49%] max-md:w-full border relative rounded-2xl flex md:gap-3 max-md:py-3 md:pt-6 md: p-3 max-md:flex flex-col'>
            <div className="flex gap-3 items-center">
                <div className='relative md:h-20 md:w-[150px] max-md:h-[100px] max-md:w-[100px] '>
                    <Image src={product?.image?.url || EmptyCover} alt='product-picture' fill className='object-cover !rounded-xl' />
                </div>
                <div className='card-details max-md:flex-col max-md:w-[60%]'>
                    <h3 className='pb-3 text-base'>{product?.title}</h3>
                    <h4 className='my-3 font-black text-black text-base'> {product?.price?.toLocaleString()} <span className='text-base font-thin text-[#62676e]'>تومان</span></h4>
                </div>
            </div>
            <div className='flex gap-3 md:w-fit mr-auto h-10 absolute left-2 bottom-2'>
                {/* <button className='bg-[#F9FAFB]  py-3 px-4 md:w-44 border rounded-xl flex items-center gap-2 text-sm hover:opacity-80 transition-all duration-300 '> <BasketCartIcon /> افزودن به سبد خرید </button> */}
                <button onClick={() => router.push(`/product/${product?.slug}`)} className='bg-[#F9FAFB]  px-2 border rounded-xl flex items-center gap-2 text-xs hover:opacity-80 transition-all duration-300'> <Eye size={18} /> </button>
                <button onClick={handleDeleteFromFavorites} className='bg-[#F9FAFB]  px-2 border rounded-xl flex items-center gap-2 text-xs hover:opacity-80 transition-all duration-300'>
                    {isDeleting ? (
                        <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                        <TrashIcon size={18} />
                    )}
                </button>
            </div>
        </div>
    )
}

export default FavoritesPageItem