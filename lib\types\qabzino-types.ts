//  interfaces for the API response structure
export interface Bill {
  Amount: number;
  BillID: string;
  BillType: string;
  BillTypeShowName: string;
  Description: string;
  Paid: boolean;
  PaymentID: string;
  RecordNumber: string;
  TraceNumber: string;
  TransactionDateTime: string;
}

export interface Parameters {
  Bills: Bill[];
  FailedBillsAmount: number;
  FailedBillsCount: number;
  RedirectLink: string;
  RedirectLinkTitle: string;
  SuccessfulBillsAmount: number;
  SuccessfulBillsCount: number;
  TotalBillsAmount: number;
  TotalBillsCount: number;
}

export interface Status {
  Code: string;
  Description: string;
}

export interface ApiResponseData {
  Parameters: Parameters;
  Status: Status;
}

export interface ApiResponse {
  success: boolean;
  data: ApiResponseData;
  status: number;
}

// Simplified data structure for UI components
export interface PaymentSummary {
  isSuccess: boolean;
  bill?: Bill;
  hasData: boolean;
  totalAmount: number;
  statusDescription: string;
  statusCode: string;
}

export interface DrivingLicensePointResponse {
  success: boolean;
  data: DrivingLicensePointData;
  status: number;
}

export interface DrivingLicensePointData {
  message: string;
  traceNumber: string;
  LicenseNumber: string;
  result: {
    AllowedToDrive: boolean;
    Point: string;
    Rule: string;
  };
  repetitive: boolean;
}

export interface CarDocumentsData {
  message: string;
  traceNumber: string;
  result: {
    CardDateTime: string;
    CardIssuanceDateTime: string;
    CardPostalBarcode: string;
    CardTitle: string;
    CardType: string;
    DocumentDateTime: string;
    DocumentIssuanceDateTime: string;
    DocumentPostalBarcode: string;
    DocumentTitle: string;
    DocumentType: string;
    IsCardSmart: boolean;
    Plaque: string;
  };
}
