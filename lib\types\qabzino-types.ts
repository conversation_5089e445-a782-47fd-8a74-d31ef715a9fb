//  interfaces for the API response structure
export interface Bill {
  Amount: number;
  BillID: string;
  BillType: string;
  BillTypeShowName: string;
  Description: string;
  Paid: boolean;
  PaymentID: string;
  RecordNumber: string;
  TraceNumber: string;
  TransactionDateTime: string;
}

export interface Parameters {
  Bills: Bill[];
  FailedBillsAmount: number;
  FailedBillsCount: number;
  RedirectLink: string;
  RedirectLinkTitle: string;
  SuccessfulBillsAmount: number;
  SuccessfulBillsCount: number;
  TotalBillsAmount: number;
  TotalBillsCount: number;
}

export interface Status {
  Code: string;
  Description: string;
}

export interface ApiResponseData {
  Parameters: Parameters;
  Status: Status;
}

export interface ApiResponse {
  success: boolean;
  data: ApiResponseData;
  status: number;
}

// Simplified data structure for UI components
export interface PaymentSummary {
  isSuccess: boolean;
  bill?: Bill;
  hasData: boolean;
  totalAmount: number;
  statusDescription: string;
  statusCode: string;
}

export interface DrivingLicensePointResponse {
  success: boolean;
  data: DrivingLicensePointData;
  status: number;
}

export interface DrivingLicensePointData {
  message: string;
  traceNumber: string;
  LicenseNumber: string;
  result: {
    AllowedToDrive: boolean;
    Point: string;
    Rule: string;
  };
  repetitive: boolean;
}

export interface CarDocumentsData {
  message: string;
  traceNumber: string;
  result: CarDocumentsResult
}
export interface CarDocumentsResult {
  CardDateTime: string;
  CardIssuanceDateTime: string;
  CardPostalBarcode: string;
  CardTitle: string;
  CardType: string;
  DocumentDateTime: string;
  DocumentIssuanceDateTime: string;
  DocumentPostalBarcode: string;
  DocumentTitle: string;
  DocumentType: string;
  IsCardSmart: boolean;
  Plaque: string;
}


export interface LicenseStatusRecord {
  Barcode: string;
  DateTime: string | null;
  FirstName: string;
  LastName: string;
  NationalID: string;
  Number: string;
  PrintConfirmDateTime: string | null;
  PrintLicenseDateTime: string | null;
  RequestDateTime: string | null;
  Status: string;
  Type: string;
  ValidityPeriodInYears: string;
}

export interface LicenseListResponse {
  success: boolean;
  data: {
    message: string;
    traceNumber: string;
    result: LicenseStatusRecord[];
  };
  status: number;
}



export interface YearsWithoutLoss {
  Driver: string;
  Health: string;
  Financial: string;
}

export interface DiscountPercent {
  Driver: string;
  Health: string;
  Financial: string;
}

export interface Vehicle {
  PlateNumber: string;
  VehicleName: string;
  EngineNumber: string;
  ChassisNumber: string;
  VIN: string;
  YearsWithoutLoss: YearsWithoutLoss;
  DiscountPercent: DiscountPercent;
}

export interface LossHistory {
  LossCount: string;
  LossSum: string;
}

export interface InsuranceResult {
  CompanyName: string;
  InsuranceNumber: string;
  UniqueInsuranceCode: string;
  InsuranceType: string;
  FullName: string;
  NationalID: string;
  StartDate: string;
  EndDate: string;
  IssueDate: string;
  Vehicle: Vehicle;
  LossHistory: LossHistory;
  PdfUrl: string;
}

export interface InsuranceStatusResponse {
  message: string;
  traceNumber: string;
  result: InsuranceResult;
}
