"use client"
import LocationIcon from "@/components/common/svg/LocationIcon"
import CustomButton from "@/components/UI/CustomButton"
import { ArrowRight, CirclePlus, SquarePen } from "lucide-react"
import { useState, useEffect } from "react";
import UserAddressItem from "./UserAddressItem";
import Map from "@/public/assets/images/map.png"
import dynamic from "next/dynamic";
import { UserAddress } from "@/lib/types/types";
import ManageAddressesModal from "./ManageAddressesModal";
import { deleteUserAddressAction } from "@/actions/userAddress.action";
import toast from "react-hot-toast";
import CheckoutProgress from "./CheckoutProgress";
import Link from "next/link";

const AddressModal = dynamic(() => import('./AddressModal'), {
    // loading: () => <p>The map is loading</p>,
    ssr: false,
});


const UserAddresses = ({
    addressList,
    selectedAddress,
    setSelectedAddress,
    onAddressListUpdate
}: {
    addressList: UserAddress[],
    selectedAddress: string,
    setSelectedAddress: (id: string) => void,
    onAddressListUpdate?: (id?: string) => void;
}) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isManageModalOpen, setIsManageModalOpen] = useState(false);
    const [editingAddress, setEditingAddress] = useState<UserAddress | null>(null);

    const handleEditAddress = (address: UserAddress) => {
        setEditingAddress(address);
        setIsModalOpen(true); // Open the address modal
    };
    

    const openModal = () => setIsModalOpen(true);
    const closeModal = () => setIsModalOpen(false);

    const openManageModal = () => setIsManageModalOpen(true);
    const closeManageModal = () => setIsManageModalOpen(false);

    const handleDeleteAddress = async (id: string) => {
        console.log("Delete address with id:", id);
        // Add delete logic here
        const deleteresponse = await deleteUserAddressAction(id);
        console.log(deleteresponse);
        if (deleteresponse.success) {
            onAddressListUpdate?.();
            toast.success("آدرس با موفقیت حذف شد");
        }
    };


    

    return (
        <div className='max-md:w-full bg-white rounded-3xl md:p-8 max-md:p-5 md:min-h-[28rem] max-md:min-h-80 '>
            <CheckoutProgress
                steps={[
                  { title: 'سبد خرید', status: 'completed' },
                  { title: 'انتخاب آدرس', status: 'current' },
                  { title: 'پرداخت', status: 'upcoming' },
                ]}
              />
            <div className="header flex justify-between">
                <div className='flex flex-col gap-3 relative pb-5 max-md:pb-3 title-bt-border'>
                    <h2 className='text-xl font-black max-md:text-base max-md:flex max-md:items-center gap-2'>
                    <Link href={'/checkout/cart'}>
                        <ArrowRight size={24} className='md:hidden' />
                    </Link>    آدرس ارسال کالا ها
                    </h2>
                </div>
                <div>
                    <LocationIcon className="max-md:w-10 max-md:h-10" size="60" />
                </div>
            </div>
            <div className="mt-5 md:w-[50%] flex md:gap-5 max-md:gap-2 ">
                <CustomButton onClick={openModal} className="py-4 px-0 max-md:px-2 max-md:py-2 max-md:text-xs">
                    <CirclePlus size={22} className="max-md:w-5" />  افزودن آدرس جدید
                </CustomButton>
                <CustomButton onClick={openManageModal} className="bg-[#F9FAFB] text-gray-500 max-md:px-0 border max-md:text-xs">
                    <SquarePen size={22} className="max-md:w-5" />  مدیریت آدرس ها
                </CustomButton>
            </div>
            <AddressModal
                open={isModalOpen}
                onClose={() => {
                    setIsModalOpen(false);
                    setEditingAddress(null); // Reset on close
                }}
                addressToEdit={editingAddress}
                onAddressCreated={onAddressListUpdate}
            />
            <ManageAddressesModal
                open={isManageModalOpen}
                onClose={closeManageModal}
                addressList={addressList}
                onDelete={handleDeleteAddress}
                onEdit={handleEditAddress}
            />
            <div className="mt-5">
                {addressList?.length ? addressList?.map((item) => (
                    <UserAddressItem
                        key={item.id}
                        id={item.id}
                        selected={selectedAddress}
                        onSelect={(id) => setSelectedAddress(id)}
                        image={Map}
                        selectable={true}
                        address={item.address}
                        province={item.province}
                        city={item.city}
                        receiver_name={item.receiver_name}
                        receiver_phone={item.receiver_phone}
                    />
                )) : <h2 className="pt-8 md:text-lg text-center"> آدرسی برای نمایش وجود ندارد </h2> }
            </div>

        </div>
    )
}

export default UserAddresses