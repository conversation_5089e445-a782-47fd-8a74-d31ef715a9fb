import React from "react";

interface BasketCartIconProps {
  width?: number | string;
  height?: number | string;
  fill?: string;
  className?: string;
}

const BasketCartIcon: React.FC<BasketCartIconProps> = ({
  width = 18.007,
  height = 18.751,
  fill = "#9DA5B0",
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 18.007 18.751"
    className={className}
    fill="none"
  >
    <g transform="translate(-3.25 -2.25)" fill={fill}>
      <path d="M10.196 13.569a.772.772 0 0 1 .772.772v2.058a.772.772 0 0 1-1.543 0v-2.058a.772.772 0 0 1 .771-.772Z" />
      <path d="M15.083 14.022a.772.772 0 1 0-1.543 0v2.058a.772.772 0 0 0 1.543 0Z" />
      <path
        fillRule="evenodd"
        d="M9.835 3.408A.772.772 0 0 0 8.5 2.636L6.44 6.2a.774.774 0 0 0-.071.166H6.08A2.83 2.83 0 0 0 4.29 11.388l.686 4.718.46 2.154a3.137 3.137 0 0 0 2.636 2.452 30.336 30.336 0 0 0 8.363 0 3.137 3.137 0 0 0 2.636-2.452l.46-2.154.686-4.718a2.83 2.83 0 0 0-1.789-5.022h-.289a.776.776 0 0 0-.072-.166L16.009 2.636a.772.772 0 1 0-1.337.772L16.38 6.366H8.127Zm8.73 8.614q-.068 0-.137 0H6.08q-.069 0-.137 0L6.5 15.833l.449 2.1a1.594 1.594 0 0 0 1.339 1.246 28.794 28.794 0 0 0 7.937 0 1.594 1.594 0 0 0 1.339-1.246l.449-2.1ZM4.793 9.2A1.286 1.286 0 0 1 6.08 7.909H18.427a1.286 1.286 0 1 1 0 2.572H6.08A1.286 1.286 0 0 1 4.793 9.2Z"
        clipRule="evenodd"
      />
    </g>
  </svg>
);

export default BasketCartIcon;
