/**
 * Simple PWA Utilities
 * Clean, readable helper functions for Progressive Web App functionality
 */

/**
 * Register the custom service worker
 */
export async function registerServiceWorker(): Promise<boolean> {
  if (!('serviceWorker' in navigator)) {
    console.warn('[PWA] Service Worker not supported');
    return false;
  }

  try {
    const registration = await navigator.serviceWorker.register('/custom-sw.js', {
      scope: '/',
    });

    console.log('[PWA] Service Worker registered successfully');

    // Handle updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            showUpdateNotification();
          }
        });
      }
    });

    return true;
  } catch (error) {
    console.error('[PWA] Service Worker registration failed:', error);
    return false;
  }
}

/**
 * Show update notification to user
 */
function showUpdateNotification(): void {
  if ('Notification' in window && Notification.permission === 'granted') {
    const notification = new Notification('خودراکس - بروزرسانی موجود', {
      body: 'نسخه جدید اپلیکیشن آماده است',
      icon: '/icon512_rounded.png',
    });

    notification.onclick = () => {
      window.location.reload();
    };
  }
}

/**
 * Check if app is running as PWA
 */
export function isPWA(): boolean {
  return window.matchMedia('(display-mode: standalone)').matches ||
         (window.navigator as unknown as { standalone?: boolean }).standalone === true ||
         document.referrer.includes('android-app://');
}

/**
 * Check if device supports PWA installation
 */
export function canInstallPWA(): boolean {
  return 'serviceWorker' in navigator &&
         'PushManager' in window &&
         'Notification' in window;
}

/**
 * Request notification permission
 */
export async function requestNotificationPermission(): Promise<NotificationPermission> {
  if (!('Notification' in window)) {
    return 'denied';
  }

  if (Notification.permission === 'default') {
    return await Notification.requestPermission();
  }

  return Notification.permission;
}

/**
 * Show simple toast notification
 */
function showToast(message: string, type: 'success' | 'error'): void {
  const toast = document.createElement('div');
  const bgColor = type === 'success' ? 'bg-green-500' : 'bg-orange-500';
  toast.className = `fixed top-4 right-4 ${bgColor} text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300`;
  toast.textContent = message;

  document.body.appendChild(toast);

  setTimeout(() => {
    toast.style.opacity = '0';
    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast);
      }
    }, 300);
  }, 3000);
}

/**
 * Show offline toast notification
 */
export function showOfflineToast(): void {
  showToast('شما آفلاین هستید', 'error');
}

/**
 * Show online toast notification
 */
export function showOnlineToast(): void {
  showToast('اتصال برقرار شد', 'success');
}

/**
 * Check if current page requires network
 */
export function isNetworkRequiredPage(pathname: string): boolean {
  const networkRequiredPatterns = [
    /\/dashboard/,
    /\/checkout/,
    /\/payment/,
    /\/car-tickets/,
    /\/motor-tickets/,
    /\/inquiry/,
    /\/wallet/,
    /\/login/,
  ];

  return networkRequiredPatterns.some(pattern => pattern.test(pathname));
}

/**
 * Redirect to offline page if needed
 */
export function handleOfflineRedirect(): void {
  if (!navigator.onLine && isNetworkRequiredPage(window.location.pathname)) {
    window.location.href = '/offline';
  }
}

/**
 * Initialize PWA functionality
 */
export async function initializePWA(): Promise<void> {
  // Register service worker
  await registerServiceWorker();

  // Request notification permission
  await requestNotificationPermission();

  // Set up offline detection
  window.addEventListener('offline', () => {
    showOfflineToast();
    handleOfflineRedirect();
  });

  window.addEventListener('online', () => {
    showOnlineToast();
  });

  console.log('[PWA] Initialization complete');
}

/**
 * Get PWA installation prompt
 */
export function setupPWAInstallPrompt(): void {
  let deferredPrompt: Event | null = null;

  window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;

    // Show custom install button
    const installButton = document.getElementById('pwa-install-button');
    if (installButton) {
      installButton.style.display = 'block';
      installButton.addEventListener('click', async () => {
        if (deferredPrompt && 'prompt' in deferredPrompt) {
          (deferredPrompt as BeforeInstallPromptEvent).prompt();
          const result = await (deferredPrompt as BeforeInstallPromptEvent).userChoice;
          console.log('[PWA] Install prompt outcome:', result.outcome);
          deferredPrompt = null;
          installButton.style.display = 'none';
        }
      });
    }
  });

  window.addEventListener('appinstalled', () => {
    console.log('[PWA] App installed successfully');
    deferredPrompt = null;
  });
}

// Type for beforeinstallprompt event
interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}
