export const dynamic = 'force-dynamic'
import { getUserNotifications } from '@/actions/notifications.action'
import AlertIcon from '@/components/common/svg/AlertIcon'
import NotificationsWrapper from '@/components/Dashboard/notifications/NotificationsWrapper'

const NotificationsPage = async () => {
    const notifications = await getUserNotifications()    
    console.log(notifications);
    
    return (
        <section className="bg-white rounded-lg md:h-[48.5rem]">
            <div className="flex items-center gap-2 pb-5 border-b p-5 mb-5">
                <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent p-2 pt-3.5 rounded-b-full">
                    <AlertIcon className="lg:size-6 size-4" />
                </div>

                <h1>
                    پیام های من
                </h1>
            </div>
            {/* <FavoritesWrapper favorites={favorites || []} /> */}
            <NotificationsWrapper notifications={notifications.data || []} />
        </section>
    )
}

export default NotificationsPage