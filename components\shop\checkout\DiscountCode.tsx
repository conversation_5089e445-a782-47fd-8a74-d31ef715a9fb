"use client"

import { useCart } from "@/lib/context/cart-context";
import toast from "react-hot-toast";
import { useState } from "react";
import { Loader2 } from "lucide-react";

interface DiscountCodeProps {
  discountCodeInput: string;
  setDiscountCodeInput: (code: string) => void;
  applyDiscount: (code: string) => void;
}

const DiscountCode = ({ discountCodeInput, setDiscountCodeInput, applyDiscount }: DiscountCodeProps) => {
  const { reloadCart } = useCart();
  const [loading, setLoading] = useState(false);

  const handleDiscountCode = async () => {
    if (!discountCodeInput) {
      toast.error("لطفا کد تخفیف را وارد کنید");
      return;
    }

    setLoading(true);
    const reloadRes = await reloadCart(discountCodeInput);
    setLoading(false);

    if (reloadRes?.success) {
      applyDiscount(discountCodeInput);
      setDiscountCodeInput("");
    } else {
      toast.error(reloadRes?.message || "خطایی رخ داده");
    }
  };

  return (
    <div className="min-h-32 md:mt-10 max-md:mt-5 bg-white rounded-3xl flex items-center justify-center">
      <div className="w-full md:px-10 max-md:px-3 relative">
        <input
          value={discountCodeInput}
          onChange={(e) => setDiscountCodeInput(e.target.value)}
          type="text"
          disabled={loading}
          className="w-full bg-[#F9FAFB] border-2 py-5 rounded-3xl placeholder-gray-500 px-10 max-md:placeholder:text-sm disabled:opacity-50"
          placeholder="کد تخفیف"
        />
        <span className="absolute right-[55px] max-md:right-[30px] top-4 max-md:top-[1.2rem] text-gray-400 text-sm">
          <span className="text-red-500 text-2xl">%</span>
        </span>

        <button
          onClick={handleDiscountCode}
          disabled={loading}
          className="bg-[#ced0d2] text-gray-700 absolute left-[70px] max-md:left-[25px] top-3 max-md:top-[14px] px-3 py-2.5 rounded-full max-md:text-sm flex items-center gap-2 disabled:opacity-80"
        >
          {loading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>در حال اعمال</span>
            </>
          ) : (
            "اعمال تخفیف"
          )}
        </button>
      </div>
    </div>
  );
};

export default DiscountCode;
