# PWA Implementation Guide - Khodrox

## Overview

This document outlines the comprehensive Progressive Web App (PWA) implementation for the Khodrox Next.js 15 application. The implementation includes custom service worker, offline caching strategies, network detection, and user-friendly error handling.

## Architecture

### 🔧 Core Components

1. **Custom Service Worker** (`/public/custom-sw.js`)
   - Replaces default Workbox-generated service worker
   - Implements multiple caching strategies
   - Handles offline error responses
   - Manages cache versioning and cleanup

2. **Network Detection Hook** (`/lib/hooks/useNetworkStatus.ts`)
   - Real-time network connectivity monitoring
   - Connection quality detection
   - Network-dependent page identification

3. **PWA Utilities** (`/lib/utils/pwa-utils.ts`)
   - Service worker management
   - PWA installation prompts
   - Offline/online notifications

4. **UI Components**
   - `NetworkStatusIndicator` - Visual network status
   - `PWAInitializer` - Service worker registration
   - `PWAInstallButton` - App installation prompt

## 📋 Caching Strategies

### Cache First Strategy
**Used for:** Static assets, Blog pages
- Checks cache first
- Falls back to network if not cached
- Ideal for content that doesn't change frequently

```javascript
// Applied to:
- /_next/static/
- /assets/
- /blog/
- Static files (.js, .css, .woff, .woff2)
```

### Network First Strategy
**Used for:** Dynamic pages, API calls
- Tries network first
- Falls back to cache if network fails
- Updates cache with fresh content

```javascript
// Applied to:
- Default for most pages
- Dynamic content
- User-specific data
```

### Stale While Revalidate
**Used for:** Images, Cacheable APIs
- Returns cached version immediately
- Updates cache in background
- Best user experience for images

```javascript
// Applied to:
- Images (.png, .jpg, .jpeg, .svg, .gif, .webp)
- /_next/image
- /api/blog/
- /api/categories/
```

### Network Only
**Used for:** Network-dependent pages
- Always requires network connection
- Shows offline page when unavailable

```javascript
// Applied to:
- /dashboard/
- /checkout/
- /payment/
- /car-tickets/
- /motor-tickets/
- /inquiry/
- /wallet/
- /login/
```

## 🚫 Offline Error Handling

### Offline Page (`/app/offline/page.tsx`)
- Persian UI with clear messaging
- Auto-reload when connection restored
- Network status indicator
- Available offline features list

### Error Responses
1. **HTML Pages**: Redirect to offline page
2. **Images**: Fallback to placeholder image
3. **API Calls**: JSON error response with offline status
4. **Static Assets**: Cached version or error

## 🔄 Network Detection

### Real-time Monitoring
- `navigator.onLine` status
- Connection quality detection
- Network change events
- Periodic connectivity tests

### User Notifications
- Toast notifications for status changes
- Visual indicators in UI
- Automatic redirects for network-dependent pages

## 📱 Installation & Setup

### 1. Service Worker Registration
The custom service worker is automatically registered via `PWAInitializer` component in the root layout.

### 2. Network Status Monitoring
Add `NetworkStatusIndicator` component to show real-time connection status.

### 3. PWA Installation
`PWAInstallButton` appears when the app can be installed as a PWA.

## 🧪 Testing Guidelines

### Offline Testing
1. **Chrome DevTools**:
   - Open DevTools → Application → Service Workers
   - Check "Offline" checkbox
   - Test different page types

2. **Network Throttling**:
   - DevTools → Network → Throttling
   - Test with "Slow 3G" and "Offline"

3. **Cache Inspection**:
   - DevTools → Application → Storage
   - Check Cache Storage for cached resources

### Page Type Testing

#### ✅ Blog Pages (Should work offline)
- Visit blog posts while online
- Go offline and revisit
- Should load from cache

#### ❌ Service Pages (Should show offline message)
- Visit dashboard/checkout while online
- Go offline and try to access
- Should redirect to offline page

#### 🖼️ Static Assets (Should be cached)
- Images, CSS, JS should load from cache
- Fallback image for uncached images

## 🔧 Configuration

### Next.js Config (`next.config.ts`)
```typescript
export default withPWA({
  dest: "public",
  register: false, // Manual registration
  skipWaiting: true,
  disable: process.env.NODE_ENV === 'development',
  sw: 'custom-sw.js',
  runtimeCaching: [], // Handled by custom SW
})(nextConfig);
```

### Manifest (`/public/manifest.json`)
- App metadata and icons
- Display mode: standalone
- Theme colors and orientation

## 📊 Performance Considerations

### Cache Management
- Automatic cache cleanup on SW updates
- Version-based cache naming
- Size limits and expiration policies

### Bundle Size
- Custom SW is lightweight (~15KB)
- No Workbox dependency in production
- Minimal runtime overhead

## 🐛 Troubleshooting

### Common Issues

1. **Service Worker Not Registering**
   - Check browser console for errors
   - Ensure HTTPS in production
   - Verify file paths

2. **Cache Not Working**
   - Clear browser cache and reload
   - Check SW cache patterns
   - Verify network requests in DevTools

3. **Offline Page Not Showing**
   - Ensure offline page is cached
   - Check network detection logic
   - Verify redirect patterns

### Debug Commands
```javascript
// Check SW registration
navigator.serviceWorker.getRegistrations()

// Check cache contents
caches.open('khodrox-v1').then(cache => cache.keys())

// Test connectivity
navigator.onLine
```

## 🚀 Deployment

### Production Checklist
- [ ] Service worker builds correctly
- [ ] Manifest.json is accessible
- [ ] HTTPS is enabled
- [ ] Cache strategies are working
- [ ] Offline page is functional
- [ ] Network detection is accurate

### Build Process
```bash
npm run build
# Custom SW is automatically copied to public/
# Manifest and icons are included
```

## 📈 Monitoring

### Analytics
- Track offline usage patterns
- Monitor cache hit rates
- Measure performance improvements

### User Feedback
- Offline experience surveys
- Error reporting for failed requests
- Installation conversion rates

## 🔄 Updates

### Service Worker Updates
- Automatic update detection
- User notification for new versions
- Graceful cache migration

### Content Updates
- Blog posts cached for offline reading
- Static assets updated automatically
- API responses refreshed when online

---

## Quick Reference

### File Structure
```
/public/
  ├── custom-sw.js          # Custom service worker
  ├── manifest.json         # PWA manifest
  └── offline/              # Offline fallback page

/lib/
  ├── hooks/
  │   └── useNetworkStatus.ts
  └── utils/
      └── pwa-utils.ts

/components/common/
  ├── PWAInitializer.tsx
  ├── NetworkStatusIndicator.tsx
  └── PWAInstallButton.tsx

/app/
  └── offline/
      └── page.tsx          # Offline error page
```

### Key Features
- ✅ Offline blog reading
- ✅ Smart caching strategies  
- ✅ Network status detection
- ✅ User-friendly error handling
- ✅ PWA installation support
- ✅ Automatic updates
- ✅ Persian UI/UX
