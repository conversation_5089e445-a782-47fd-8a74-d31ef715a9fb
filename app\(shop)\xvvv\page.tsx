// app/env-debug/page.tsx

export const dynamic = 'force-dynamic'; // allow reading env on server

import ClientEnvDisplay from '@/components/ClientEnvDisplay';
import React from 'react';


const EnvDebugPage = () => {
  // Server-only environment variables
  const serverEnv = {
    BASE_URL: process.env.BASE_URL,
    BASE_URL_2: process.env.BASE_URL_2,
    NODE_ENV: process.env.NODE_ENV,
    X_APPLICATION_TOKEN: process.env.X_APPLICATION_TOKEN,
  };

  return (
    <div className="p-6 max-w-3xl mx-auto">
      {
        serverEnv && (
          <section>
            <h2 className="text-lg font-semibold mb-2 text-blue-700">🔒 Server Environment Variables</h2>
            <ul className="bg-blue-50 rounded p-4 text-sm font-mono">
              {Object.entries(serverEnv).map(([key, value]) => (
                <li key={key}>
                  <strong>{key}</strong>: {value}
                </li>
              ))}
            </ul>
          </section>
        )
      }
      <ClientEnvDisplay />
    </div>
  );
};

export default EnvDebugPage;
