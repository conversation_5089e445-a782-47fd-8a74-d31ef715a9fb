import { CartApiItem } from '@/lib/context/cart-context'
import AccordionHeader from '../mainPage/AccordionHeader'
import OrderSummaryList from './OrderSummaryList'
import React from 'react';
import clsx from 'clsx';
export interface OrderSummaryProps {
        items?: CartApiItem[];
        total?: number
        total_discount?: number
        user_id?: string
        subtotal?: number
        className?: string
}
const OrderSummary: React.FC<OrderSummaryProps> = ({ items, total, total_discount, user_id, subtotal ,className }) => {
    return (
        <section className={`${className} bg-white mt-10 px-8 max-md:px-3 rounded-3xl pb-3` }>
            <AccordionHeader buttonStyles="top-[3px]" title='خلاصه سفارش' titleSize='!md:text-xl max-md:text-base'>
                <div>
                    <p className='bg-[#FFF5D8] p-2 border-2 border-[#F7BC06] border-dashed max-md:text-sm leading-8 rounded-xl'>
                        برای دریافت فاکتور، بعد از دریافت سفارش به حساب کاربری و صفحه جزئیات سفارش سر بزنید
                    </p>
                </div>
                <OrderSummaryList
                className=''
                    items={items}
                    total={total}
                    total_discount={total_discount}
                    user_id={user_id}
                    subtotal={subtotal}
                />
            </AccordionHeader>
        </section>
    )
}

export default OrderSummary