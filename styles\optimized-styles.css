/* Optimized styles.css - Only essential styles */

html {
    scroll-behavior: smooth;
}

.broken-div {
    clip-path: polygon(0 100%, 100% 100%, 100% 0, 50% 20%, 0 0);
}

/* Swiper and Leaflet CSS moved to separate minimal files */

/* Swiper styles moved to minimal-swiper.css */

/* Active tab styles - used in components */
.active-tab {
    background-color: #1F84FB;
    color: #fff;
    padding: 7px 10px;
    border-radius: 12px;
}

/* Table styles - used in data display */
table {
    width: 100%;
    border: 1px solid #d1d5db;
    background-color: #ffffff;
    font-size: 0.875rem;
    border-collapse: collapse;
}

table th,
table td {
    border: 1px solid #d1d5db;
    padding: 0.5rem;
    text-align: center;
}

table tbody tr:nth-child(odd) {
    background-color: #f3f4f6;
}

/* About service styles - used in content sections */
@media (min-width: 768px) {
    .about-service h2 {
        font-weight: 900;
        font-size: 1.5rem;
        line-height: 2rem;
    }

    .about-service p {
        line-height: 2rem;
    }

    .about-service div h2 {
        line-height: 2rem;
    }
}

@media not all and (width >= 768px) {
    .about-service p {
        font-size: 15px;
        line-height: 28px;
    }

    .about-service h3 {
        font-size: 1.125rem;
    }
}
