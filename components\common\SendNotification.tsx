"use client"
import { useEffect, useState } from "react"
import toast from "react-hot-toast"
import { sendNotificationToken } from "@/actions/other.action"

const NOTIFICATION_PREFERENCE_KEY = 'khodrox_notification_preference';

interface FirebaseMessagePayload {
    notification?: {
        title?: string;
        body?: string;
        image?: string;
    };
    data?: Record<string, string>;
    from: string;
    collapseKey?: string;
    messageId: string;
}

/**
 * Dynamic Firebase notification service loader
 * This component handles the dynamic loading of Firebase dependencies to reduce bundle size
 */
interface NotificationServiceLoader {
    init: () => Promise<void>;
    getToken: () => Promise<string>;
    onForegroundMessage: (callback: (payload: FirebaseMessagePayload) => void) => void;
}

/**
 * Dynamically loads Firebase notification dependencies
 * @returns Promise<NotificationServiceLoader> - The notification service instance
 */
/**
 * Loads Firebase notification service using function-based approach
 * This is more readable and friendly than class-based approach
 * @returns Promise<NotificationServiceLoader> - The notification service instance
 */
async function loadFirebaseNotificationService(): Promise<NotificationServiceLoader> {
    try {
        // Dynamically import Firebase dependencies using the new utility functions
        const [
            { registerFirebaseSw },
            { createNotificationService },
            { firebaseConfig, firebaseVapidKey }
        ] = await Promise.all([
            import("@/lib/notification/registerSW"),
            import("@/lib/notification/notificationUtils"),
            import("@/lib/firebase/config")
        ]);

        // Register service worker first
        const registration = await registerFirebaseSw();

        if (!registration) {
            throw new Error('Failed to register service worker');
        }

        // Create and return the notification service using function-based approach
        // This is more readable and friendly than class-based approach
        const notifService = createNotificationService(
            firebaseConfig,
            firebaseVapidKey || ""
        );

        return notifService;
    } catch (error) {
        console.error('Failed to load Firebase notification service:', error);
        throw error;
    }
}

const SendNotification = () => {
    const [showPrompt, setShowPrompt] = useState(false);
    const [isVisible, setIsVisible] = useState(false); // Controls animation state

    useEffect(() => {
        // Function to check notification preferences
        const checkNotificationPreference = () => {
            // Check if browser supports notifications
            if (typeof window === 'undefined' || !('Notification' in window)) {
                return false;
            }

            // If permission is already granted or denied by browser, respect that
            if (Notification.permission === 'granted' || Notification.permission === 'denied') {
                return false;
            }

            // Check user preference in localStorage
            const preferenceStr = localStorage.getItem(NOTIFICATION_PREFERENCE_KEY);

            if (!preferenceStr) {
                // No preference set, should show the prompt
                return true;
            }

            try {
                // Try to parse the preference
                const preference = JSON.parse(preferenceStr);

                // Check if it's a rejection and if it's still valid
                if (preference.status === 'rejected') {
                    // If it was rejected more than 2 days ago, we can show the prompt again
                    const rejectionTime = preference.timestamp;
                    const currentTime = Date.now();
                    const twoDaysInMs = 2 * 24 * 60 * 60 * 1000;

                    if (currentTime - rejectionTime >= twoDaysInMs) {
                        // It's been more than 2 days, remove the preference and show the prompt
                        localStorage.removeItem(NOTIFICATION_PREFERENCE_KEY);
                        return true;
                    } else {
                        // Still within the 2-day window, don't show the prompt
                        return false;
                    }
                } else if (preference.status === 'accepted') {
                    // If the user has accepted, we can show the prompt
                    return true;
                }
            } catch {
                // If there's an error parsing, just remove the item and show the prompt
                localStorage.removeItem(NOTIFICATION_PREFERENCE_KEY);
                return true;
            }

            return false;
        };

        // Only set up the timeout if we should show the notification
        const shouldShowNotification = checkNotificationPreference();

        let timeoutId: NodeJS.Timeout | null = null;

        if (shouldShowNotification) {
            // Make sure we start with the notification hidden
            setShowPrompt(false);
            setIsVisible(false);

            // Set a timeout to show the notification after 4 seconds
            timeoutId = setTimeout(() => {
                console.log('Showing notification prompt after delay');
                setShowPrompt(true);

                // Add a small delay before starting the animation
                setTimeout(() => {
                    setIsVisible(true);
                }, 10);
            }, 2000);
        }

        // Clean up function to clear the timeout if the component unmounts
        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        };
    }, []); // No dependencies needed since we load Firebase dynamically

    const handleAcceptNotification = async () => {
        try {
            setShowPrompt(false);

            // Dynamically load Firebase notification service
            const service = await loadFirebaseNotificationService();

            // Initialize the service
            await service.init();

            if (Notification.permission !== 'granted') {
                return;
            }

            const token = await service.getToken();
            const path = window.location.pathname;

            // Get user agent
            const userAgent = navigator.userAgent || '';

            try {
                const result = await sendNotificationToken(
                    token,
                    path,
                    "accept",
                    userAgent
                );

                if (!result.success) {
                    console.error('Failed to send notification token to server');
                }
            } catch (error) {
                console.error('Error sending notification token to server:', error);
                await sendNotificationToken(token, path, "reject", userAgent);
            }

            service.onForegroundMessage((payload: FirebaseMessagePayload) => {
                if (payload.notification) {
                    toast.success(payload.notification.title || 'پیام جدید');
                    if (payload.notification.body) {
                        toast.success(payload.notification.body);
                    }
                }
            });

            localStorage.setItem(NOTIFICATION_PREFERENCE_KEY, JSON.stringify({
                status: 'accepted',
                timestamp: Date.now()
            }));

            setShowPrompt(false);
            // toast.success('اعلان‌ها با موفقیت فعال شدند');
        } catch (error) {
            console.error('Error setting up notifications:', error);
            toast.error('خطا در فعال‌سازی اعلان‌ها');
        }
    };

    const handleRejectNotification = async () => {
        localStorage.setItem(NOTIFICATION_PREFERENCE_KEY, JSON.stringify({
            status: 'rejected',
            timestamp: Date.now()
        }));

        const path = window.location.pathname;

        // Get user agent
        const userAgent = navigator.userAgent || '';

        await sendNotificationToken("", path, "reject", userAgent);

        setShowPrompt(false);
        // toast.error('اعلان‌ها غیرفعال شدند');
    };

    if (!showPrompt) {
        return null;
    }

    return (
        <div className={`
            fixed right-4 max-md:right-3 max-md:left-3 p-4 text-white bg-slate-700 rounded-lg shadow-lg max-w-sm z-50
            transition-all duration-500 ease-in-out
            ${isVisible ? 'bottom-4 opacity-100' : '-bottom-20 opacity-0'}
        `}>
            <p className="mb-4 max-md:text-sm">برای دریافت اطلاعیه‌های مهم و به‌روزرسانی‌ها، اعلان‌ها را فعال کنید.</p>
            <div className="flex gap-3 justify-end">
                <button
                    onClick={handleRejectNotification}
                    className="bg-gray-300 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
                    // disabled={isLoading}
                >
                    بعداً
                </button>
                <button
                    onClick={handleAcceptNotification}
                    className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center"
                    // disabled={isLoading}
                >
                    فعال‌سازی

                </button>
            </div>
        </div>
    )
}

export default SendNotification