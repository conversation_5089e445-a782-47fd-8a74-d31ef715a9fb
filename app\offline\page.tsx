/**
 * Simple Offline Error Page Component
 * Displays when users try to access network-dependent features while offline
 */

'use client';

import Link from 'next/link';
import { useEffect } from 'react';

/**
 * Simple offline page component with Persian UI
 */
export default function OfflinePage() {
  useEffect(() => {
    // Auto-reload when connection is restored
    const handleOnline = () => {
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    };

    window.addEventListener('online', handleOnline);
    return () => window.removeEventListener('online', handleOnline);
  }, []);
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-[#F5F6F8] flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        {/* Offline Icon */}
        <div className="mb-8">
          <div className="w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <svg 
              className="w-12 h-12 text-gray-400" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M18.364 5.636l-12.728 12.728m0-12.728l12.728 12.728M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2z" 
              />
            </svg>
          </div>
        </div>

        {/* Main Message */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          شما آفلاین هستید
        </h1>
        
        <p className="text-gray-600 mb-6 leading-relaxed">
          این عملیات نیاز به اتصال اینترنت دارد. لطفاً اتصال خود را بررسی کنید و دوباره تلاش کنید.
        </p>

        {/* Network Status Indicator */}
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-center">
            <div className="w-3 h-3 bg-orange-400 rounded-full mr-2 animate-pulse"></div>
            <span className="text-orange-700 text-sm font-medium">
              در انتظار اتصال اینترنت...
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={() => window.location.reload()}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
          >
            تلاش مجدد
          </button>
          
          <Link
            href="/"
            className="block w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6 rounded-lg transition-colors duration-200"
          >
            بازگشت به صفحه اصلی
          </Link>
        </div>

        {/* Offline Features Info */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-medium text-blue-900 mb-2">
            امکانات آفلاین
          </h3>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• مطالعه مقالات بلاگ</li>
            <li>• مشاهده صفحات بازدید شده</li>
            <li>• دسترسی به محتوای ذخیره شده</li>
          </ul>
        </div>


      </div>
    </div>
  );
}
