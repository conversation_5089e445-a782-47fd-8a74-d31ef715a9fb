"use client"
// import { useEffect, useReducer, use<PERSON><PERSON>back, useRef, useMemo } from "react"
import { Dialog, DialogContent } from "@/components/UI/dialog"
// import { Input } from "@/components/UI/input"
// import { Button } from "@/components/UI/button"
// import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap, useMapEvents } from "react-leaflet"
import "leaflet/dist/leaflet.css"
import { DialogTitle } from "@radix-ui/react-dialog"
import { XIcon } from "lucide-react"
import { DialogClose } from "@radix-ui/react-dialog"
// import { Label } from "@/components/UI/label"
// import L from 'leaflet'
// import toast from "react-hot-toast"
// import { createUserAddressAction } from "@/actions/userAddress.action"
import { UserAddress } from "@/lib/types/types"
import dynamic from "next/dynamic"
const NeshanAddressForm = dynamic(() => import('../address/NeshanAddressForm'), {
    loading: () => <p>در حال بارگذاری</p>,
    ssr: false,
});

// const defaultPosition: [number, number] = [35.6892, 51.3890] // Tehran center

/**
 * Address form data structure matching API requirements
 */
export interface AddressFormData {
    id?:string
    name: string;
    receiver_name: string;
    receiver_phone: string;
    is_recipient_self: boolean;
    province: string;
    city: string;
    zip_code: string;
    address: string;
    latitude: number;
    longitude: number;
}

/**
 * UI state for managing loading states and form interactions
 */
// interface UIState {
//     isReverseGeocodingInProgress: boolean;
//     isAddressChangeInProgress: boolean;
//     position: [number, number];
// }

/**
 * Combined state for the address modal
//  */
// interface AddressModalState {
//     formData: AddressFormData;
//     uiState: UIState;
// }

// /**
//  * Action types for the reducer
//  */
// type AddressModalAction =
//     | { type: 'UPDATE_FIELD'; field: keyof AddressFormData; value: string | number | boolean }
//     | { type: 'UPDATE_COORDINATES'; latitude: number; longitude: number }
//     | { type: 'UPDATE_POSITION'; position: [number, number] }
//     | { type: 'SET_REVERSE_GEOCODING'; isInProgress: boolean }
//     | { type: 'SET_ADDRESS_CHANGE'; isInProgress: boolean }
//     | { type: 'UPDATE_ADDRESS_FROM_GEOCODING'; province: string; city: string; address: string }
//     | { type: 'RESET_FORM' };

/**
 * Initial state for the address modal
 */
// const initialState: AddressModalState = {
//     formData: {
//         name: "",
//         receiver_name: "",
//         receiver_phone: "",
//         is_recipient_self: true,
//         province: "",
//         city: "",
//         zip_code: "",
//         address: "",
//         latitude: defaultPosition[0],
//         longitude: defaultPosition[1],
//     },
//     uiState: {
//         isReverseGeocodingInProgress: false,
//         isAddressChangeInProgress: false,
//         position: defaultPosition,
//     }
// };

export default function AddressModal({
    open,
    onClose,
    onAddressCreated,
    addressToEdit,
    // setSelectedAddress 
}: {
    open: boolean;
    onClose: () => void;
    onAddressCreated?: (id?: string) => void;
    addressToEdit?: UserAddress | null;
    // setSelectedAddress: (id: string) => void;
}) {
    // const [state, dispatch] = useReducer(addressModalReducer, initialState);

    // // Destructure state for easier access
    // const { formData, uiState } = state;
    // const {
    //     name,
    //     receiver_name,
    //     receiver_phone,
    //     is_recipient_self,
    //     province,
    //     city,
    //     zip_code,
    //     address
    // } = formData;

    // const {
    //     isReverseGeocodingInProgress,
    //     position
    // } = uiState;

    // /**
    //  * Helper function to update form fields
    //  */
    // const updateField = useCallback((field: keyof AddressFormData, value: string | number | boolean) => {
    //     dispatch({ type: 'UPDATE_FIELD', field, value });
    // }, []);

    // // Fix Leaflet marker icon issue in Next.js
    // useEffect(() => {
    //     // Only run on client side
    //     if (typeof window !== 'undefined') {
    //         // @ts-expect-error - Known issue with Leaflet in Next.js
    //         delete L.Icon.Default.prototype._getIconUrl;

    //         L.Icon.Default.mergeOptions({
    //             iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
    //             iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
    //             shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
    //         });
    //     }
    // }, []);

    // // Forward geocoding: Convert address text to coordinates
    // const handleAddressChange = useCallback(async () => {
    //     // Skip if we're currently processing a reverse geocoding request
    //     if (isReverseGeocodingInProgress || !province || !city) return;

    //     dispatch({ type: 'SET_ADDRESS_CHANGE', isInProgress: true });
    //     try {
    //         const query = `${province} ${city} ${address}`;
    //         const res = await fetch(`https://api.neshan.org/v5/search?term=${encodeURIComponent(query)}&lat=35.6892&lng=51.3890`, {
    //             headers: {
    //                 "Api-Key": process.env.NEXT_PUBLIC_NESHAN_API_KEY || "",
    //             },
    //         });
    //         const data = await res.json();
    //         if (data.topics && data.topics.length > 0) {
    //             const { location } = data.topics[0];
    //             dispatch({
    //                 type: 'UPDATE_POSITION',
    //                 position: [parseFloat(location.y), parseFloat(location.x)]
    //             });
    //         }
    //     } catch (error) {
    //         console.error("Error in forward geocoding:", error);
    //     } finally {
    //         dispatch({ type: 'SET_ADDRESS_CHANGE', isInProgress: false });
    //     }
    // }, [province, city, address, isReverseGeocodingInProgress]);

    // // Reverse geocoding: Convert coordinates to address
    // const handleLocationSelected = useCallback(async (lat: number, lng: number) => {
    //     dispatch({ type: 'SET_REVERSE_GEOCODING', isInProgress: true });
    //     try {
    //         // Use OpenStreetMap's Nominatim service for reverse geocoding
    //         const response = await fetch(
    //             `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`,
    //             {
    //                 headers: {
    //                     "Accept-Language": "fa", // Request Persian results
    //                     "User-Agent": "KhodRox Address Picker" // Required by Nominatim's terms
    //                 }
    //             }
    //         );

    //         const data = await response.json();

    //         if (data && data.address) {
    //             // Extract address components
    //             const {
    //                 state,
    //                 city,
    //                 town,
    //                 village,
    //                 suburb,
    //                 neighbourhood,
    //                 road,
    //                 house_number
    //             } = data.address;

    //             // Determine province and city
    //             const detectedProvince = state || "";
    //             let detectedCity = "";
    //             if (city) {
    //                 detectedCity = city;
    //             } else if (town) {
    //                 detectedCity = town;
    //             } else if (village) {
    //                 detectedCity = village;
    //             }

    //             // Build detailed address
    //             let detailedAddress = "";
    //             if (suburb) detailedAddress += suburb + " ";
    //             if (neighbourhood) detailedAddress += neighbourhood + " ";
    //             if (road) detailedAddress += road + " ";
    //             if (house_number) detailedAddress += "پلاک " + house_number;

    //             // Update all address fields at once
    //             dispatch({
    //                 type: 'UPDATE_ADDRESS_FROM_GEOCODING',
    //                 province: detectedProvince,
    //                 city: detectedCity,
    //                 address: detailedAddress.trim()
    //             });
    //         }
    //     } catch (error) {
    //         console.error("Error in reverse geocoding:", error);
    //     } finally {
    //         dispatch({ type: 'SET_REVERSE_GEOCODING', isInProgress: false });
    //     }
    // }, []);

    // // Update map when address fields change
    // useEffect(() => {
    //     if ((province || city || address) && !isReverseGeocodingInProgress) {
    //         handleAddressChange();
    //     }
    // }, [province, city, address, handleAddressChange, isReverseGeocodingInProgress]);

    // const handleCreateAddress = async () => {
    //     // Prepare the final data object matching API requirements
    //     const addressData: AddressFormData = {
    //         name: formData.name,
    //         receiver_name: formData.receiver_name,
    //         receiver_phone: formData.receiver_phone,
    //         is_recipient_self: formData.is_recipient_self,
    //         province: formData.province,
    //         city: formData.city,
    //         zip_code: formData.zip_code,
    //         address: formData.address,
    //         latitude: formData.latitude,
    //         longitude: formData.longitude
    //     };



    //     const createAddressResponse = await createUserAddressAction(addressData);
    //     console.log(createAddressResponse);


    //     dispatch({ type: 'RESET_FORM' });
    //     if (createAddressResponse.success) {
    //         toast.success("ادرس جدید با موفقیت ایجاد شد")
    //         // Notify parent component to refresh address list
    //         onAddressCreated?.();
    //     } else {
    //         toast.error(createAddressResponse.message)
    //     }
    //     onClose();
    // }

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent className="md:w-full max-md:max-w-[93%] max-w-4xl bg-white p-0 z-50 rounded-lg shadow-lg [&>button]:hidden max-h-[85vh] top-[50.5vh] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar]:absolute [&::-webkit-scrollbar]:right-0" dir="rtl" >
                {/* Modal Header */}
                <div className="flex items-center justify-between border-b bg-white p-4 max-md:p-3 rounded-t-lg flex-shrink-0">
                    <DialogTitle className="text-xl font-semibold max-md:text-lg">افزودن آدرس جدید</DialogTitle>
                    <div className="bg-gradient-to-t from-gray-100 to-transparent py-2.5 px-2 max-md:py-2 max-md:px-1.5 rounded-b-full">
                        <DialogClose asChild>
                            <button onClick={onClose} className="text-gray-600 border-2 border-gray-400 rounded-full p-1 hover:text-gray-400 focus:outline-none">
                                <XIcon className="h-6 w-6" />
                            </button>
                        </DialogClose>
                    </div>
                </div>

                {/* <AddressForm
                //  open={open}
                 onClose={onClose}
                 onAddressCreated={onAddressCreated}
                 addressToEdit={addressToEdit}
                 /> */}
                <NeshanAddressForm
                //  open={open}
                 onClose={onClose}
                 onAddressCreated={onAddressCreated}
                 addressToEdit={addressToEdit}
                //  setSelectedAddress={setSelectedAddress}
                 />
            </DialogContent  >
        </Dialog>
    )
}
