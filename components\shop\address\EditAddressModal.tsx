'use client';

import { <PERSON><PERSON><PERSON>, Trash } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/UI/dialog';
import EditAddressForm from '@/components/Header/EditAddress';
import { UserAddress } from '@/lib/types/types';

interface EditAddressModalProps {
  isModalOpen: boolean;
  onClose: () => void;
  addresses: UserAddress[];
  selectedAddressId: string | null;
  setSelectedAddressId: (id: string | null) => void;
  setAddresses: React.Dispatch<React.SetStateAction<UserAddress[]>>;
  modalMode: 'select' | 'edit';
  setModalMode: (mode: 'select' | 'edit') => void;
  addressToEdit: UserAddress | null;
  setAddressToEdit: (address: UserAddress | null) => void;
  handleUpdateAddress: (updatedAddress: UserAddress) => void;
}

/**
 * Reusable modal component for address selection and editing
 * Can be used across different parts of the application
 */
const EditAddressModal = ({
  isModalOpen,
  onClose,
  addresses,
  selectedAddressId,
  setSelectedAddressId,
  setAddresses,
  modalMode,
  setModalMode,
  addressToEdit,
  setAddressToEdit,
  handleUpdateAddress,
}: EditAddressModalProps) => {
  return (
    <Dialog open={isModalOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90vw] max-w-2xl rounded-2xl p-4">
        <DialogHeader>
          <DialogTitle className="text-center py-3">
            {modalMode === 'select' ? 'انتخاب آدرس' : 'ویرایش آدرس'}
          </DialogTitle>
        </DialogHeader>

        {modalMode === 'select' ? (
          <div className="flex flex-col gap-3 max-h-[580px] overflow-y-auto">
            {addresses.map((item) => (
              <div
                key={item.id}
                className={`border rounded-xl p-3 flex items-center justify-between cursor-pointer ${
                  selectedAddressId === item.id ? 'border-primary bg-primary/5' : ''
                }`}
                onClick={() => setSelectedAddressId(item.id)}
              >
                <div className="flex items-start gap-2">
                  <input
                    type="radio"
                    checked={selectedAddressId === item.id}
                    onChange={() => setSelectedAddressId(item.id)}
                  />
                  <div className="flex flex-col gap-2">
                    {item.receiver_name && <span className="font-semibold">{item.receiver_name}</span>}
                    {/* <span className="text-sm text-gray-600">{item.address}</span> */}
                    {item.address && <span className="text-xs text-gray-500">{item.address}</span>}
                    {item.receiver_phone && <span className="text-xs text-gray-500">{item.receiver_phone}</span>}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <button
                    type="button"
                    className="text-green-500 hover:text-green-600"
                    onClick={(e) => {
                      e.stopPropagation();
                      setAddressToEdit(item);
                      setModalMode('edit');
                    }}
                  >
                    <Pencil size={18} />
                  </button>
                  <button
                    type="button"
                    className="text-red-500 hover:text-red-600"
                    onClick={(e) => {
                      e.stopPropagation();
                      setAddresses(prev => prev.filter(addr => addr.id !== item.id));
                      if (selectedAddressId === item.id) {
                        setSelectedAddressId(null);
                      }
                    }}
                  >
                    <Trash size={18} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
            // TODO: fix this shit as soon as possible
          // <AddressForm />
          ""
        )}

        {/* ساخت آدرس جدید Button */}
        {modalMode === 'select' && (
          <div className="flex justify-start mt-4">
            <button
              onClick={() => {
                setAddressToEdit(null);
                setModalMode('edit');
              }}
              className="flex items-center gap-2 text-green-600 font-bold"
            >
              <span className="text-2xl">+</span>
              ساخت آدرس جدید
            </button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EditAddressModal;