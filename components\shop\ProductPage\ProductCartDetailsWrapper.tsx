"use client"
import { ProductData } from "@/lib/types/product.types";
import dynamic from "next/dynamic"
const ProductCartDetails = dynamic(() => import('./ProductCartDetails'), {
  loading: () => <div className=''></div>,
  ssr: false,
});
const ProductCartDetailsWrapper = ({productData}: {productData: ProductData}) => {
  return (
    <ProductCartDetails productData={productData} />
  )
}

export default ProductCartDetailsWrapper