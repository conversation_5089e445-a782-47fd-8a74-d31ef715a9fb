import React from 'react';

interface PaginationProps {
  currentPage: number;
  lastPage: number;
  onPageChange: (page: number) => void;
}

const Pagination = ({ currentPage, lastPage, onPageChange }: PaginationProps) => {
  const getPageNumbers = () => {
    const totalNumbers = 5; // تعداد عددی‌هایی که نشون داده میشه
    const totalBlocks = totalNumbers + 2; // شامل اول و آخر

    if (lastPage <= totalBlocks) {
      return Array.from({ length: lastPage }, (_, i) => i + 1);
    }

    const pages: (number | 'dots')[] = [];

    const leftBound = Math.max(2, currentPage - 1);
    const rightBound = Math.min(lastPage - 1, currentPage + 1);

    const showLeftDots = leftBound > 2;
    const showRightDots = rightBound < lastPage - 1;

    pages.push(1);

    if (showLeftDots) {
      pages.push('dots');
    }

    for (let i = leftBound; i <= rightBound; i++) {
      pages.push(i);
    }

    if (showRightDots) {
      pages.push('dots');
    }

    pages.push(lastPage);

    return pages;
  };

  const pages = getPageNumbers();

  return (
    <div className="flex justify-center mt-6 flex-wrap">
      {pages.map((page, index) =>
        page === 'dots' ? (
          <span key={index} className="mx-1 px-3 py-1 text-gray-500">...</span>
        ) : (
          <button
            key={index}
            onClick={() => onPageChange(page)}
            className={`mx-1 px-3 py-1 rounded-full transition-colors duration-200 ${
              currentPage === page
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            {page}
          </button>
        )
      )}
    </div>
  );
};

export default Pagination;
