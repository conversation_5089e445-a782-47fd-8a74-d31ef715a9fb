export const dynamic = 'force-dynamic';
import ShippingPageClient from "@/components/shop/checkout/ShippingPageClient"
import { DeliveryMethodsResponse, UserAddressesResponse } from "@/lib/types/types"
import { getDeliveryMethods, getUserAddresses } from "@/lib/utils"
import { ArrowRight } from "lucide-react";
import Link from "next/link";

const CheckoutShippingPage = async () => {
  const userAddresses:UserAddressesResponse = await getUserAddresses()
  const deliveryMethods: DeliveryMethodsResponse = await getDeliveryMethods() 
  console.log(deliveryMethods);
  

  return (
    <main className='container mx-auto  mb-16'>
        <div className='px-1 mt-8 flex items-center gap-2' >
         <Link href={'/checkout/cart'} type='button' className='bg-white cursor-not-allowed max-md:hidden rounded-lg px-4 py-1.5 flex items-center gap-3'>
           <ArrowRight className=' max-md:hidden' size={26} /> بازگشت
         </Link>
        </div>

       <ShippingPageClient userAddresses={userAddresses} deliveryMethods={deliveryMethods?.data || []} />

    </main>
  )
}

export default CheckoutShippingPage