"use client";

import useInquiryPost from "@/lib/hooks/useInquiryPost";
import { useRef, useState } from "react";
import PlateInputCar, { PlateInputRef } from "@/components/UI/CarPlateInput";
import {
  NATIONAL_CODE_MAX_LENGTH,
  PHONE_NUMBER_MAX_LENGTH,
} from "@/lib/constants";

import PlateInputMotor from "@/components/UI/MotorPlateInput";
import { plateNumberIsNotValid } from "@/lib/validations";

import Card from "@/components/common/Card";
import InquiryHeader from "@/components/inquiry/InquiryHeader";
import CustomInput from "@/components/UI/CustomInput";
import IdCardIcon from "@/components/common/svg/IdCardIcon";
import ChoiceWrapper from "@/components/common/ChoiceWrapper";
import CustomButton from "@/components/UI/CustomButton";
import StatusMessage from "@/components/common/StatusMessage";
import { ServiceStatusType } from "@/lib/types/types";
import DisplayServiceIcon from "@/components/Services/DisplayServiceIcon";
import InquiryInsuranceIcon from "@/components/common/svg/services/InquiryInsuranceIcon";
import { BookText, ChevronsLeft } from "lucide-react";
import { getCarIdDocumentsInquiry } from "@/actions/inquiry.action";
import { useRouter } from "nextjs-toploader/app";
import toast from "react-hot-toast";

type Props = {
  isMotor: boolean;
  status: ServiceStatusType;
};

const CAR = "خودرو";
const MOTOR_CYCLE = "موتور سیکلت";

interface CarIdDocumentsInquiryType {
  plateNumber: string[];
  nationalCode: string;
  phoneNumber: string;
  // insuranceNumber: string;
}

interface FormErrors {
  plateNumber?: string;
  nationalCode?: string;
  phoneNumber?: string;
  insuranceNumber?: string;
}

export default function CarIdDocumentsForm({ isMotor, status }: Props) {
  // const { mutate, isLoading } = useInquiryPost();
  const [isLoading, setIsLoading] = useState(false);
  const [hasTriedSubmit, setHasTriedSubmit] = useState(false);
  const router = useRouter();

  const [steps, setSteps] = useState(1);
  const vehicleType = isMotor ? MOTOR_CYCLE : CAR;

  // Form state
  const [formData, setFormData] = useState<CarIdDocumentsInquiryType>({
    plateNumber: isMotor ? ["", ""] : ["", "", "", ""],
    nationalCode: "",
    phoneNumber: "",
    // insuranceNumber: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});

  // Refs
  const nationalCodeRef = useRef<HTMLInputElement | null>(null);
  // const insuranceNumberRef = useRef<HTMLInputElement | null>(null);
  const phoneNumberRef = useRef<HTMLInputElement | null>(null);
  const plateInputRef = useRef<PlateInputRef | null>(null);

  // Form handlers
  const handleBackBtn = () => {
    if (steps === 2) {
      setSteps(1);
    }
  };

  const handleFirstStep = () => {
    const isPlateInvalid = plateNumberIsNotValid(formData.plateNumber, isMotor);
    debugger
    if (steps === 1 && !isPlateInvalid) {
      setSteps(2);
      // setErrors(prev => ({ ...prev, plateNumber: undefined }));
      return;
    } else {
      setErrors((prev) => ({
        ...prev,
        plateNumber: "شماره پلاک را صحیح وارد کنید",
      }));
    }
  };

  const validateForm = (): boolean => {
    debugger
    
    const newErrors: FormErrors = {};

    // Validate national code
    if (
      !formData.nationalCode ||
      formData.nationalCode.length !== NATIONAL_CODE_MAX_LENGTH
    ) {
      newErrors.nationalCode = "کد ملی باید 10 رقم باشد";
    }

    // Validate phone number
    if (
      !formData.phoneNumber ||
      formData.phoneNumber.length !== PHONE_NUMBER_MAX_LENGTH
    ) {
      newErrors.phoneNumber = "شماره موبایل باید 11 رقم باشد";
    }

    // Validate insurance number
    // if (!formData.insuranceNumber) {
    //   newErrors.insuranceNumber = "شماره بیمه الزامی است";
    // }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setHasTriedSubmit(true);
    debugger;
    if (validateForm()) {
      // Transform form data to ViolationQueryParams format
      const violationParams = {
        left: formData.plateNumber[0] || "",
        middle: isMotor ? undefined : formData.plateNumber[2],
        right: isMotor
          ? formData.plateNumber[1] || ""
          : formData.plateNumber[3] || "",
        alphabet: isMotor ? undefined : formData.plateNumber[1],
        isMotor: isMotor ? ("true" as const) : ("false" as const),
        withDetails: "true" as const,
        phoneNumber: formData.phoneNumber,
        nationalCode: formData.nationalCode,
        inquiry: "false" as const,
        reInquiry: "false" as const,
      };

      console.log("Form submitted:", violationParams);
      const data = {
        details: {
          national_id: formData.nationalCode,
          phone: formData.phoneNumber,
        },
        plaque: {
          left: formData.plateNumber[0] || "",
          mid: formData.plateNumber[2],
          right: formData.plateNumber[3] || "",
          alphabet: formData.plateNumber[1],
        },
      };
      console.log(data);
      const response = await getCarIdDocumentsInquiry(data);
      console.log(response);
      if (response.success) {
        router.push(
          `/carid-documents/result?trace_number=${response.data.traceNumber}`
        );
      } else if (response.status === 402) {
        // console.error('API returned error:', response.error);
        // TODO: Show error message to user
        localStorage.setItem("walletMessage", response.data.message);
        router.push(
          `/wallet?left=${data.plaque.left}&alphabet=${data.plaque.alphabet}&middle=${data.plaque.mid}&right=${data.plaque.right}&national_id=${data.details.national_id}&mobile_number=${data.details.phone}`
        );
      } else if (response.status === 401) {
    const query = new URLSearchParams({
     left: data.plaque.left,
     alphabet: encodeURIComponent(data.plaque.alphabet), 
     middle: data.plaque.mid,
     right: data.plaque.right,
     national_id: data.details.national_id,
     mobile_number: data.details.phone,
     type: "carid_documents",
   });

   router.push(`/login?${query.toString()}`);


      } else {
        toast.error(
          response.data.mesage || "خطایی رخ داده است. لطفا مجددا تلاش کنید."
        );
      }

      // setIsLoading(true)
      // response.then(res => {
      //   setIsLoading(false)
      //   console.log(res);
      // })
    }
  };

  // Input change handlers
  const handlePlateChange = (plateNumber: string[]) => {
    setFormData((prev) => ({ ...prev, plateNumber }));
    setErrors((prev) => ({ ...prev, plateNumber: undefined }));
  };

  const handleInputChange =
    (field: keyof CarIdDocumentsInquiryType) => (value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    };

  return (
    <form onSubmit={handleSubmit} className="mt-5 md:mt-10" autoComplete="off">
      <Card className="!px-0 !pt-5 !pb-10  mt-5">
        {steps > 1 && (
          <div className="md:px-14 text-left flex justify-end my-2">
            <CustomButton
              onClick={handleBackBtn}
              className="w-fit bg-transparent text-gray-600 flex flex-row-reverse p-0"
              type="button"
            >
              <ChevronsLeft size={20} /> بازگشت
            </CustomButton>
          </div>
        )}
        <InquiryHeader
          icon={
            <div className="w-[50px] h-[50px] flex justify-center items-center">
              <DisplayServiceIcon
                containerWidth={33}
                containerHeight={33}
                variant="green"
                status="ACTIVE"
                border={false}
                shadow={false}
              >
                <InquiryInsuranceIcon width={25} height={25} />
              </DisplayServiceIcon>
            </div>
          }
          title={`استعلام بیمه شخص ثالث ${vehicleType}`}
        />

        <div className="px-5 mt-2 md:px-14">
          {steps === 1 ? (
            <>
              <div>
                {isMotor ? (
                  <PlateInputMotor
                    value={formData.plateNumber}
                    onChange={handlePlateChange}
                    ref={plateInputRef}
                  />
                ) : (
                  <PlateInputCar
                    value={formData.plateNumber}
                    onChange={handlePlateChange}
                    ref={plateInputRef}
                  />
                )}
                {errors.plateNumber && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.plateNumber}
                  </p>
                )}
              </div>
            </>
          ) : null}

          <div className="mt-8">
            {steps === 2 ? (
              <>
                <div>
                  <label className="text-[#596068] text-xs block mb-2">
                    {`کد ملی مالک ${vehicleType}`}
                  </label>
                  <CustomInput
                    variant="secondary"
                    allowOnlyNumbers
                    maxLength={NATIONAL_CODE_MAX_LENGTH}
                    placeholder="کد ملی مالک را وارد کنید"
                    value={formData.nationalCode}
                    onChange={handleInputChange("nationalCode")}
                    ref={nationalCodeRef}
                    leftIcon={<IdCardIcon height={20} width={20} />}
                    direction="rtl"
                    inputMode="numeric"
                    autoFocus
                  />
                  {errors.nationalCode && hasTriedSubmit && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.nationalCode}
                    </p>
                  )}
                </div>
                {/* <div className="mt-4">
                  <label className="text-[#596068] text-xs block mb-2">
                    {`شماره بیمه مالک ${vehicleType}`}
                  </label>
                  <CustomInput
                    variant="secondary"
                    allowOnlyNumbers
                    leftIcon={
                      <BookText
                        width={20}
                        height={20}
                        className="text-gray-400"
                      />
                    }
                    placeholder="شماره بیمه مالک را وارد کنید"
                    value={formData.insuranceNumber}
                    onChange={handleInputChange('insuranceNumber')}
                    ref={insuranceNumberRef}
                    inputMode="numeric"
                    direction="rtl"
                  />
                  {errors.insuranceNumber && (
                    <p className="text-red-500 text-xs mt-1">{errors.insuranceNumber}</p>
                  )}
                </div> */}
                <div className="mt-4">
                  <label className="text-[#596068] text-xs block mb-2">
                    {`شماره موبایل مالک ${vehicleType}`}
                  </label>
                  <CustomInput
                    variant="secondary"
                    allowOnlyNumbers
                    maxLength={PHONE_NUMBER_MAX_LENGTH}
                    leftIcon={
                      <BookText
                        width={20}
                        height={20}
                        className="text-gray-400"
                      />
                    }
                    placeholder="شماره موبایل مالک را وارد کنید"
                    value={formData.phoneNumber}
                    onChange={handleInputChange("phoneNumber")}
                    ref={phoneNumberRef}
                    inputMode="numeric"
                    direction="rtl"
                  />
                  {errors.phoneNumber && hasTriedSubmit && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.phoneNumber}
                    </p>
                  )}
                </div>
              </>
            ) : null}
            <div className="mt-5">
              <ChoiceWrapper backgroundColor="#FFF5D8" borderColor="#F7BC06">
                <div className="flex py-3 flex-col gap-y-1">
                  <p className="text-[#5E646B] text-sm">
                    هزینه استعلام: 16,170 تومان
                  </p>
                  <p className="text-[#5E646B] text-sm">
                    خودراکس هیچ دخل و تصرفی در تعیین این هزینه ندارد
                  </p>
                </div>
              </ChoiceWrapper>
            </div>

            {steps === 1 ? (
              <CustomButton
                onClick={handleFirstStep}
                className="mt-5 !py-5"
                type="button"
              >
                تایید پلاک
              </CustomButton>
            ) : (
              <CustomButton
                loading={isLoading}
                disabled={
                  isLoading
                  // status === "DISABLED" ||
                  // status === "COMING_SOON"
                }
                onClick={handleSubmit}
                className="mt-5 !py-5"
              >
                استعلام
              </CustomButton>
            )}

            {(status === "DISABLED" || status === "COMING_SOON") && (
              <div className="mt-5">
                <StatusMessage status={status} />
              </div>
            )}
          </div>
        </div>
      </Card>
    </form>
  );
}
