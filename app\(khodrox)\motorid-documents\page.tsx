import BlogComments from '@/components/blog/SinglePage/BlogComments'
import CarIdDocumentsFormWrapper from '@/components/CarIdDocuments/CarIdDocumentsFormWrapper'
import ChildSchema from '@/components/common/ChildSchema'
import ArticleSection from '@/components/InquiryStaticComponents/ArticleSection'
import Faq from '@/components/InquiryStaticComponents/Faq'
// import envConfig from '@/lib/config-env'
import { PageContentResponse } from '@/lib/types/types'
import { getPageContent } from '@/lib/utils'


export async function generateMetadata() {
    const data = await getPageContent("motorid-documents");

    return {
        title: data.meta_title,
        description: data.meta_description,
        keywords: data.tags || [],
        ...(data.meta_search && { robots: data.meta_search }),
        ...(data.canonical && { alternates: { canonical: data.canonical } }),
    };
}


export const revalidate = 0

const isMotor: boolean = true
// const withDetails: boolean | undefined = false


const MotorIdDocomentsPage = async () => {
    // const env = envConfig()
    const status = process.env.NEXT_PUBLIC_MOTORID_DOCUMENTS!
    const data: PageContentResponse = await getPageContent("motorid-documents")
    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id='motorid-documents'
                    schema={schema}
                />
            }
            <CarIdDocumentsFormWrapper
                title={title || ""}
                isMotor={isMotor}
                status={status}
            />

            {
                description &&
                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
            <div className="md:max-w-7xl mx-auto mb-10 px-3">
                  {/* <UserComments /> */}
                  <BlogComments contentId={data.id} />
            </div>
        </>
    )
}

export default MotorIdDocomentsPage