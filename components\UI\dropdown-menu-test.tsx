"use client"

import React from 'react'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from './dropdown-menu-lite'

/**
 * Test component to verify the lightweight dropdown menu works
 * This mimics the usage pattern in UserLoggedInButton
 */
export default function DropdownMenuTest() {
  const handleItemClick = (item: string) => {
    console.log('Clicked:', item)
  }

  return (
    <div className="p-8">
      <h2 className="text-lg font-semibold mb-4">Dropdown Menu Test</h2>
      
      {/* Test 1: Basic dropdown similar to UserLoggedInButton */}
      <div className="mb-8">
        <h3 className="text-md font-medium mb-2">Test 1: User Menu Style</h3>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <span>
              <div className="flex min-w-[80px] min-h-[40px] cursor-pointer border border-blue-500/50 bg-blue-500/10 rounded-[99px] p-[5px] gap-1 items-center justify-center">
                <svg width="15" height="15" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="6,9 12,15 18,9"></polyline>
                </svg>
                <div className="text-left flex justify-center items-end gap-1 flex-col">
                  <span className="font-bold text-xs text-blue-500/80">09123456789</span>
                  <span className="flex justify-end items-center gap-1">
                    <span className="text-xs font-base font-bold">
                      <span className='text-xs mx-1'>تومان</span>
                      50000
                    </span>
                  </span>
                </div>
                <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
              </div>
            </span>
          </DropdownMenuTrigger>
          <DropdownMenuContent className='md:w-56 w-[200px] z-[900] max-md:text-sm'>
            <div className="bg-gray-100 w-full p-3 rounded-lg my-1 text-sm">
              <div className="flex items-center gap-2">
                <span>کیف پول:</span>
                <p className="relative">
                  <span className="text-green-500 max-md:text-sm">50000</span>
                  <span className="text-sm">تومان</span>
                </p>
              </div>
            </div>
            <DropdownMenuItem onClick={() => handleItemClick('افزایش اعتبار')}>
              <div className="text-gray-600">💳</div>
              <span>افزایش اعتبار</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleItemClick('تاریخچه استعلام')}>
              <div className="text-gray-600">📋</div>
              <span>تاریخچه استعلام</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleItemClick('داشبورد من')}>
              <div className="text-gray-600">📊</div>
              <span>داشبورد من</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => handleItemClick('خروج')}>
              <div className="text-gray-600">🚪</div>
              <span>خروج</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Test 2: Simple dropdown similar to SendAsDropdown */}
      <div className="mb-8">
        <h3 className="text-md font-medium mb-2">Test 2: Send As Style</h3>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="rounded-xl border px-4 py-2 text-sm text-gray-500 hover:bg-gray-100 transition">
              ارسال با نام شما
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="text-right">
            <DropdownMenuItem onClick={() => handleItemClick('ارسال با نام شما')}>
              ارسال با نام شما
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleItemClick('ارسال ناشناس')}>
              ارسال ناشناس
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Test 3: With labels and separators */}
      <div className="mb-8">
        <h3 className="text-md font-medium mb-2">Test 3: With Labels</h3>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
              Menu with Labels
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Account</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => handleItemClick('Profile')}>
              Profile
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleItemClick('Settings')}>
              Settings
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => handleItemClick('Logout')}>
              Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
