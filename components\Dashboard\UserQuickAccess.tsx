import { MoveLeft } from "lucide-react";
import Link from "next/link";

const UserQuickAccess = ({inquirieslength, orderslength, favoritesslength, addresseslength}: {inquirieslength: number, orderslength: number, favoritesslength: number, addresseslength: number}) => {
  return (
    <div className="md:grid flex max-md:pb-2 max-md:pr-4 items-start max-md:overflow-x-auto md:grid-cols-1 max-md:grid-cols-2 lg:grid-cols-4 gap-4 px-3 md:px-0">
      {/* سفارشات من */}
      <Link href="/dashboard/order-history" className="w-full p-4 flex items-center gap-4 bg-white shadow-md rounded-2xl border border-gray-200">
        <div className="flex items-center gap-3 w-full">
          <div className="shrink-0">
            <div className="bg-[#FFD141] rounded-full p-2 flex items-center justify-center text-white w-12 h-12">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007ZM8.625 10.5a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm7.5 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                />
              </svg>
            </div>
          </div>
          <div className="flex items-center justify-between w-full">
            <div className="flex flex-col gap-1">
              <h4 className="whitespace-nowrap text-sm md:text-base">سفارشات من</h4>
              <h5 className="text-[#FFD141] text-lg md:text-xl">
                {orderslength} <span className="text-gray-400 text-sm md:text-base">سفارش</span>
              </h5>
            </div>
            <MoveLeft className="size-4 max-md:hidden" />
          </div>
        </div>
      </Link>

      {/* استعلامات من */}
      <Link href="/dashboard/inquiry-history" className="w-full p-4 flex items-center gap-4 bg-white shadow-md rounded-2xl border border-gray-200">
        <div className="flex items-center gap-3 w-full">
          <div className="shrink-0">
            <div className="bg-[#31CAFD] rounded-full p-2 flex items-center justify-center text-white w-12 h-12">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M16.5 6v.75m0 3v.75m0 3v.75m0 3V18m-9-5.25h5.25M7.5 15h3M3.375 5.25c-.621 0-1.125.504-1.125 1.125v3.026a2.999 2.999 0 0 1 0 5.198v3.026c0 .621.504 1.125 1.125 1.125h17.25c.621 0 1.125-.504 1.125-1.125v-3.026a2.999 2.999 0 0 1 0-5.198V6.375c0-.621-.504-1.125-1.125-1.125H3.375Z"
                />
              </svg>
            </div>
          </div>
          <div className="flex items-center justify-between w-full">
            <div className="flex flex-col gap-1">
              <h4 className="whitespace-nowrap text-sm md:text-base">استعلام های من</h4>
              <h5 className="text-[#24AFF6] text-lg md:text-xl">
                {inquirieslength} <span className="text-gray-400 text-sm md:text-base">استعلام</span>
              </h5>
            </div>
            <MoveLeft className="size-4 max-md:hidden" />
          </div>
        </div>
      </Link>

      {/* علاقه مندی های من */}
      <Link href="/dashboard/favorites" className="w-full p-4 flex items-center gap-4 bg-white shadow-md rounded-2xl border border-gray-200">
        <div className="flex items-center gap-3 w-full">
          <div className="shrink-0">
            <div className="bg-[#FE6C49] rounded-full p-2 flex items-center justify-center text-white w-12 h-12">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"
                />
              </svg>
            </div>
          </div>
          <div className="flex items-center justify-between w-full">
            <div className="flex flex-col gap-1">
              <h4 className="whitespace-nowrap text-sm md:text-base">علاقه مندی ها</h4>
              <h5 className="text-[#FE6C49] text-lg md:text-xl">
                {favoritesslength} <span className="text-gray-400 text-sm md:text-base">مورد</span>
              </h5>
            </div>
            <MoveLeft className="size-4 max-md:hidden" />
          </div>
        </div>
      </Link>

      {/* آدرس های من */}
      <Link href="/dashboard/user-addresses" className="w-full p-4 flex items-center gap-4 bg-white shadow-md rounded-2xl border border-gray-200">
        <div className="flex items-center gap-3 w-full">
          <div className="shrink-0">
            <div className="bg-[#A887C0] rounded-full p-2 flex items-center justify-center text-white w-12 h-12">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"
                />
              </svg>
            </div>
          </div>
          <div className="flex items-center justify-between w-full">
            <div className="flex flex-col gap-1">
              <h4 className="whitespace-nowrap text-sm md:text-base">آدرس های من</h4>
              <h5 className="text-[#A887C0] text-lg md:text-xl">
                {addresseslength} <span className="text-gray-400 text-sm md:text-base">آدرس</span>
              </h5>
            </div>
            <MoveLeft className="size-4 max-md:hidden" />
          </div>
        </div>
      </Link>
    </div>
  );
};

export default UserQuickAccess;