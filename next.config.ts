import type { NextConfig } from "next";
import bundleAnalyzer from "@next/bundle-analyzer";
import withP<PERSON> from "next-pwa";

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === "true",
});

const nextConfig: NextConfig = withBundleAnalyzer({
  reactStrictMode: false,
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "www.ikea.com",
        pathname: "/de/en/images/products/**",
      },
      {
        protocol: "https",
        hostname: "files.virgool.io",
        pathname: "/upload/**",
      },
      {
        protocol: "https",
        hostname: "us.e-cloth.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "satvikworld.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "www.satvikworld.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "khodrox.iranisoft.ir",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "picsum.photos",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "shop-khodrox.liara.run",
        pathname: "/**",
      },
       {
        protocol: "https",
        hostname: "s3.ayanco.com",
        pathname: "/**",
      },
    ],
    domains: ["localhost", "dkstatics-public.digikala.com", "dl.khodrox.com",'trustseal.enamad.ir','s3.ayanco.com'],
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
});

export default withPWA({
  dest: "public",
  register: false, // We'll register our custom SW manually
  skipWaiting: true,
  disable: process.env.NODE_ENV === 'development', // Disable in development
  sw: 'custom-sw.js', // Use our custom service worker
  runtimeCaching: [], // Disable default runtime caching (we handle it in custom SW)
})(nextConfig);