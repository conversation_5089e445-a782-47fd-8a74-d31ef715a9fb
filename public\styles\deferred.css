/* Deferred CSS - Non-critical styles loaded after page load */

@tailwind components;
@tailwind utilities;

/* Article section styles */
.article-section h1 {
    font-size: 32px !important;
    font-weight: 700 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h2 {
    font-size: 28px !important;
    font-weight: 600 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h3 {
    font-size: 24px !important;
    font-weight: 600 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h4 {
    font-size: 20px !important;
    font-weight: 500 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h5 {
    font-size: 16px !important;
    font-weight: 400 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h6 {
    font-size: 14px !important;
    font-weight: 400 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

/* Figure styles */
figure {
    margin: 0 auto;
}
figure img {
    border-radius: 5%;
    width: 100% !important;
}

/* Blockquote styles */
blockquote {
  background-color: #F5F6F8;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
  border-radius: 1.5rem;
  background-image: url('/assets/images/double-comma.png');
  background-repeat: no-repeat;
  background-size: 70px auto;
  background-position: 90px 40px;
}

@media (max-width: 768px) {
  blockquote {
    width: 96%;
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    background-image: none;
    background-position: 40px 90px;
  }
}

blockquote p {
  text-align: justify;
  line-height: 2rem;
  color:#302e2e !important;
}

@media (max-width: 768px) {
  blockquote p {
    line-height: 1.75rem;
    color:#302e2e !important;
  }
}

/* SEO Section styles */
.SeoSection h2{
    padding-right: 1.5rem;
    border-right: 0.3rem solid #1F84FB;
}
.SeoSection a {
    color: #1F84FB;
}

.SeoSection figure {
  overflow-y: auto;
  max-width: 100%;
}

.SeoSection ul {
    list-style: none;
    padding-right: 1.5rem;
}
  
.SeoSection ul li {
    position: relative;
    padding-right: 1rem;
    line-height: 1.6;
    margin-right: 20px;
    margin-bottom: 10px;
}
  
.SeoSection ul li::before {
    content: '•';
    position: absolute;
    right: -15px;
    top: 65%;
    transform: translateY(-50%);
    color: #1F84FB;
    font-size: 2.5em;
    line-height: 1;
}
  
.SeoSection ul li[data-list="checked"]::before {
    content: '';
    width: 0.6em;
    height: 0.6em;
    border: 2px solid #1F84FB;
    border-radius: 50%;
    background: transparent;
    font-size: initial;
}
  
.SeoSection ol li {
    list-style: decimal;
    margin-bottom: 1rem;
}
.SeoSection ol {
    padding-right: 1.5rem;
}
.SeoSection ol li::marker {
    color: #3c53c7;
    font-weight: bold;
    font-size: 1.2rem;
}

.blog-pluses::before {
    content: "";
    position: absolute;
    top: 5px;
    right: -1px;
    width: 65px;
    height: 95%;
    background-image: url("/assets/images/pluses.png");
    background-repeat: repeat-y;
    background-position: right;
}

.ltr {
  direction: ltr;
}

.bg-gray-hero {
  background-color: #f5f5f5; 
  background-image: url('/assets/images/bg-gray.webp');
  background-position: bottom;
  background-size: cover;
  background-repeat: no-repeat;
}

/* Active tab styles */
.active-tab {
    background-color: #1F84FB;
    color: #fff;
    padding: 7px 10px;
    border-radius: 12px;
}

/* Table styles */
table {
    width: 100%;
    border: 1px solid #d1d5db;
    background-color: #ffffff;
    font-size: 0.875rem;
    border-collapse: collapse;
}

table th,
table td {
    border: 1px solid #d1d5db;
    padding: 0.5rem;
    text-align: center;
}

table tbody tr:nth-child(odd) {
    background-color: #f3f4f6;
}

/* About service styles */
@media (min-width: 768px) {
    .about-service h2 {
        font-weight: 900;
        font-size: 1.5rem;
        line-height: 2rem;
    }

    .about-service p {
        line-height: 2rem;
    }

    .about-service div h2 {
        line-height: 2rem;
    }
}

@media not all and (width >= 768px) {
    .about-service p {
        font-size: 15px;
        line-height: 28px;
    }

    .about-service h3 {
        font-size: 1.125rem;
    }

    .blog-pluses::before {
        display: none;
    }
}
