'use client'

import React, { useEffect, useState } from 'react'
import { Camera } from 'lucide-react'
import JalaliDatePicker from '@/components/UI/JalaliDatePicker'
import CustomButton from '@/components/UI/CustomButton'
import { UserProfileData } from '@/lib/types/types'
import { Value } from 'react-multi-date-picker'
import Image from 'next/image'
import { getUserProfile, updateUserProfile } from '@/actions/userProfile.action'
import toast from 'react-hot-toast'
import LocationIcon from '@/components/common/svg/LocationIcon'

export const convertPersianToEnglishNumbers = (input: string): string => {
    const persianNumbers = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹']
    const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']

    return input.replace(/[۰-۹]/g, w => englishNumbers[persianNumbers.indexOf(w)])
}

interface CompleteProfileBoxProps  {
    formData: UserProfileData;
    setFormData: React.Dispatch<React.SetStateAction<UserProfileData>>;
    isLoading: boolean;
}

const CompleteProfileBox = ({ formData, setFormData, isLoading }: CompleteProfileBoxProps ) => {    
    // const [profileImageFile, setProfileImageFile] = useState<File | null>(null)
    const [isSubmitting, setIsSubmitting] = useState(false) // Loading state for form submission

    // useEffect(() => {
    //     async function getUser() {
    //         try {
    //             setIsLoading(true)
    //             const user = await getUserProfile()
    //             console.log(user);

    //             setFormData({
    //                 full_name: user?.data?.full_name || '',
    //                 phone: user?.data?.phone || '',
    //                 email: user?.data?.email || '',
    //                 national_code: user?.data?.national_code || '',
    //                 shamsi_birth_date: user?.data?.shamsi_birth_date || '',
    //                 // profile_image: user?.data?.profile_image || '',
    //             })
    //         } catch (error) {
    //             console.error('Error fetching user profile:', error)
    //             toast.error("خطا در بارگذاری اطلاعات کاربر")
    //         } finally {
    //             setIsLoading(false)
    //         }
    //     }
    //     getUser()

    // }, [])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { id, value } = e.target
        setFormData(prev => ({ ...prev, [id]: value }))
    }


    // Show loading spinner while fetching user data
    if (isLoading) {
        return (
            <div className="flex flex-col gap-4 bg-white p-6 rounded-2xl shadow-md min-h-[533px]">
                <div className="flex items-center justify-center h-full min-h-[400px]">
                    <div className="flex flex-col items-center gap-4">
                        <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
                        <p className="text-gray-600 text-lg">در حال بارگذاری اطلاعات...</p>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className="flex flex-col gap-4 bg-white p-6 max-md:p-3 max-md:py-6 rounded-2xl shadow-md min-h-[533px] max-md:w-full">
            <div className="header flex justify-between">
                <div className='flex flex-col gap-3 relative pb-5 max-md:pb-3 title-bt-border'>
                    <h2 className='text-xl font-black max-md:text-base'> تکمیل پروفایل </h2>
                    <p> برای ادامه خرید لطفا پروفایل خود را تکمیل کنید </p>
                </div>
                {/* <div>
                    <LocationIcon className="max-md:w-10 max-md:h-10" size="60" />
                </div> */}
            </div>
            <div className="flex flex-wrap justify-between w-full gap-5 h-full md:w-[90%] max-md:w-full max-md:flex max-md:flex-col max-md:gap-4">
                {/* Left form fields */}
                    <div className="flex flex-col gap-3 md:w-[46%]">
                        <label className="max-md:px-1" htmlFor="full_name">نام و نام خانوادگی</label>
                        <input
                            type="text"
                            id="full_name"
                            value={formData.full_name}
                            onChange={handleChange}
                            className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                            disabled={isSubmitting}
                        />
                    </div>

                    <div className="flex flex-col gap-3 md:w-[46%]">
                        <label className="max-md:px-1" htmlFor="phone">شماره تلفن</label>
                        <input
                            type="text"
                            id="phone"
                            value={formData.phone}
                            onChange={handleChange}
                            className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                            disabled={isSubmitting}
                        />
                    </div>

                    <div className="flex flex-col gap-3 md:w-[46%]">
                        <label className="max-md:px-1" htmlFor="email">ایمیل</label>
                        <input
                            type="email"
                            id="email"
                            value={formData.email}
                            onChange={handleChange}
                            className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                            disabled={isSubmitting}
                        />
                    </div>
                    <div className="flex flex-col gap-3 md:w-[46%]">
                        <label className="max-md:px-1" htmlFor="shamsi_birth_date">تاریخ تولد</label>
                        <JalaliDatePicker
                            value={formData.shamsi_birth_date || ''}
                            setValue={(val: Value) =>
                                setFormData(prev => ({ ...prev, shamsi_birth_date: val?.toString?.() ?? '' }))
                            }
                        // disabled={isSubmitting}
                        />
                    </div>

                    <div className="flex flex-col gap-3 md:w-[46%]">
                        <label className="max-md:px-1" htmlFor="national_code">کد ملی</label>
                        <input
                            placeholder="کد ملی"
                            type="text"
                            id="national_code"
                            value={formData.national_code}
                            onChange={handleChange}
                            className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                            disabled={isSubmitting}
                        />
                    </div>
                

               
            </div>

            {/* Date + national code */}
            <div className="md:grid grid-cols-2 gap-x-10 h-full md:w-[90%] mt-5 max-md:flex max-md:flex-col max-md:gap-4">
            </div>

           
        </div>
    )
}

export default CompleteProfileBox