"use client";

import { useRouter } from "nextjs-toploader/app";
import { useEffect, useRef, useState } from "react";
import PlateInputCar, {
  carPlatePartsMaxLengths,
  PlateInputRef,
} from "@/components/UI/CarPlateInput";
import { useForm } from "react-hook-form";
import {
  InsuranceInquiryFormSchema,
  InsuranceInquiryType,
} from "@/lib/types/zod-schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  CAR_PLATE_RIGHT,
  MOTOR_PLATE_RIGHT,
  NATIONAL_CODE_MAX_LENGTH,
  PHONE_NUMBER_MAX_LENGTH,
} from "@/lib/constants";
import PlateInputMotor, {
  motorPlatePartsMaxLengths,
} from "@/components/UI/MotorPlateInput";
import {
  isValidIranianNationalCode,
  plateNumberIsNotValid,
} from "@/lib/validations";
import {
  Form,
  FormControl,
  FormField,
  FormI<PERSON>,
  FormLabel,
  FormMessage,
} from "@/components/UI/form";
import Card from "@/components/common/Card";
import InquiryHeader from "@/components/inquiry/InquiryHeader";
import CustomInput from "@/components/UI/CustomInput";
import IdCardIcon from "@/components/common/svg/IdCardIcon";
import ChoiceWrapper from "@/components/common/ChoiceWrapper";
import CustomButton from "@/components/UI/CustomButton";
import StatusMessage from "@/components/common/StatusMessage";
import { ServiceStatusType } from "@/lib/types/types";
import DisplayServiceIcon from "@/components/Services/DisplayServiceIcon";
import InquiryInsuranceIcon from "@/components/common/svg/services/InquiryInsuranceIcon";
import { BookText, ChevronsLeft } from "lucide-react";
import { getCarInsuranceInquiry } from "@/actions/inquiry.action";
import toast from "react-hot-toast";

type Props = {
  isMotor: boolean;
  status: ServiceStatusType;
};
interface FormErrors {
  plateNumber?: string;
  nationalCode?: string;
  phoneNumber?: string;
  insuranceNumber?: string;
}

const CAR = "خودرو";
const MOTOR_CYCLE = "موتور سیکلت";

export default function InsuranceFormContainer({ isMotor, status }: Props) {
  // const { mutate, isLoading } = useInquiryPost();
  const router = useRouter();
  const [steps, setSteps] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const vehicleType = isMotor ? MOTOR_CYCLE : CAR;
  const nationalCodeRef = useRef<HTMLInputElement | null>(null);
  const insuranceNumberRef = useRef<HTMLInputElement | null>(null);
  const phoneNumberRef = useRef<HTMLInputElement | null>(null);
  const plateInputRef = useRef<PlateInputRef | null>(null);

  const form = useForm<InsuranceInquiryType>({
    mode: "onSubmit",
    resolver: zodResolver(InsuranceInquiryFormSchema),
    defaultValues: {
      plateNumber: isMotor ? ["", ""] : ["", "", "", ""],
      nationalCode: "",
      insuranceNumber: "",
      phoneNumber: "",
    },
  });

  useEffect(() => {
    const subscription = form.watch((values) => {
      const plateNumber = values.plateNumber;
      const nationalCode = values.nationalCode;
      const insuranceNumber = values.insuranceNumber;

      if (
        !nationalCode &&
        ((!isMotor &&
          plateNumber?.[CAR_PLATE_RIGHT]?.length ===
          carPlatePartsMaxLengths[CAR_PLATE_RIGHT]) ||
          (isMotor &&
            plateNumber?.[MOTOR_PLATE_RIGHT]?.length ===
            motorPlatePartsMaxLengths[MOTOR_PLATE_RIGHT]))
      ) {
        nationalCodeRef.current?.focus();
      }

      if (
        document.activeElement === nationalCodeRef.current &&
        !insuranceNumber &&
        nationalCode?.length === NATIONAL_CODE_MAX_LENGTH
      ) {
        insuranceNumberRef.current?.focus();
      }
    });

    return () => subscription.unsubscribe(); // Cleanup on unmount
  }, [isMotor, form]); // `watch` reference is stable, so this won't cause extra re-renders.
  const handleBackBtn = () => {
    if (steps == 2) {
      setSteps(1);
    }
  };

  const handleFirstStep = () => {
    debugger
    console.log(plateNumberIsNotValid(form.getValues("plateNumber"), isMotor));
    if (
      steps == 1 &&
      !plateNumberIsNotValid(form.getValues("plateNumber"), isMotor)
    ) {
      form.clearErrors("plateNumber");
      
    } else {
      form.setError("plateNumber", { message: "شماره پلاک را صحیح وارد کنید" });
    }
   
    const nationalId = form.getValues("nationalCode");
     const newErrors: FormErrors = {};
    
    if (nationalId) {
      const isNationalCodeValid = isValidIranianNationalCode(nationalId);
      if (!isNationalCodeValid) {
        newErrors.nationalCode = "کد ملی را صحیح وارد کنید";
        setErrors(newErrors);
      } 
    } else {
      newErrors.nationalCode = "کد ملی الزامی است";
      setErrors(newErrors);
    }
   
    setErrors(newErrors);
    if (Object.keys(newErrors).length === 0) {
      setSteps(2);
      return
    }
  };
  const validateForm = (): boolean => {
    // debugger

    const newErrors: FormErrors = {};

    
    const insuranceNumber = form.getValues("insuranceNumber");
    const nationalId = form.getValues("nationalCode");

    
    if (nationalId) {
      const isNationalCodeValid = isValidIranianNationalCode(nationalId);
      if (!isNationalCodeValid) {
        newErrors.nationalCode = "کد ملی را صحیح وارد کنید";
        setErrors(newErrors);
      }
    } else {
      newErrors.nationalCode = "کد ملی الزامی است";
      setErrors(newErrors);
    }
    if (insuranceNumber) {
      if (insuranceNumber.length < 8) {
        newErrors.insuranceNumber = "شماره بیمه نامه نامعتبر است";
        setErrors(newErrors);
      }
    } else {
      newErrors.insuranceNumber = "شماره بیمه نامه الزامی است";
      setErrors(newErrors);
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  async function onSubmit() {
    if (isLoading || status === "DISABLED" || status === "COMING_SOON") return;
    debugger
    // if (plateNumberIsNotValid(plateNumber, isMotor)) {
    //   form.setError("plateNumber", { message: "شماره پلاک را صحیح وارد کنید" });
    //   return;
    // }
    setIsLoading(true)

    const plaque = form.getValues("plateNumber");
    const insuranceNumber = form.getValues("insuranceNumber");
    const nationalId = form.getValues("nationalCode");
    const PhoneNumber = form.getValues("phoneNumber");

    // const isPlateInvalid = plateNumberIsNotValid(plaque.plateNumber, isMotor);
    // if (nationalId) {
    //   const isNationalCodeValid = isValidIranianNationalCode(nationalId);
    //   if (!isNationalCodeValid) {
    //     form.setError("nationalCode", { message: "کد ملی را صحیح وارد کنید" });
    //     return;
    //   }
    // }
    // if (insuranceNumber) {
    //   if (insuranceNumber.length < 8) {
    //     form.setError("insuranceNumber", {
    //       message: "شماره بیمه نامه نامعتبر است",
    //     });
    //     return;
    //   }
    // }
    // if (PhoneNumber) {
    //   if (PhoneNumber.length !== 11) {
    //     form.setError("phoneNumber", {
    //       message: "شماره موبایل نامعتبر است",
    //     });
    //     return;
    //   }
    // }
    if (!validateForm()) {
      setIsLoading(false)
      return;
    }
    const data = {
      details: {
        national_id: nationalId,
        // phone: PhoneNumber,
        unique_insurance_policy: insuranceNumber,
      },
      plaque: {
        left: plaque[0] || "",
        mid: plaque[2],
        right: plaque[3] || "",
        alphabet: plaque[1],
      },
    };
    const response = await getCarInsuranceInquiry(data);
    console.log("response:", response);
    if (response?.success) {
      return router.push(
        `/car-insurance/result?trace_number=${response.data.traceNumber}`
      );
    } else if (response.status === 402) {
      // console.error('API returned error:', response.error);
      // TODO: Show error message to user
      localStorage.setItem("walletMessage", response.data.message);
      return router.push(
        `/wallet?left=${data.plaque.left}&alphabet=${data.plaque.alphabet}&middle=${data.plaque.mid}&right=${data.plaque.right}&national_id=${data.details.national_id}`
      );
    } else if (response.status === 401) {
      const query = new URLSearchParams({
        left: data.plaque.left,
        alphabet: data.plaque.alphabet,
        middle: data.plaque.mid,
        right: data.plaque.right,
        national_id: data.details.national_id,
        // mobile_number: data.details.phone,
        unique_insurance_policy: data.details.unique_insurance_policy,
        type: "car_insurance",
      });

      return router.push(`/login?${query.toString()}`);


    } else {
      setIsLoading(false)
      toast.error(
        response.data.message || "خطایی رخ داده است. لطفا مجددا تلاش کنید."
      );
    }
    setIsLoading(false)


    // const mutateResult = await mutate({
    //     withDetails: isWithInfo ? 'true' : 'false',
    //     phoneNumber: isWithInfo ? phone : "",
    //     nationalCode: isWithInfo ? nationalId : "",
    //     inquiry: 'true',
    //     middle: isMotor ? "" : plaque[CAR_PLATE_MIDDLE],
    //     left: isMotor ? plaque[MOTOR_PLATE_LEFT] : plaque[CAR_PLATE_LEFT],
    //     right: isMotor ? plaque[MOTOR_PLATE_RIGHT] : plaque[CAR_PLATE_RIGHT],
    //     alphabet: isMotor ? "" : plaque[CAR_PLATE_ALPHABET],
    //     isMotor: isMotor ? 'true' : 'false',
    //     reInquiry: 'false'
    // })
    //
    // if (!mutateResult.success && mutateResult.href) {
    //     router.push(mutateResult.href)
    // } else if (!mutateResult.success && mutateResult.message) {
    //     toast.error(mutateResult.message);
    // } else if (mutateResult.success && mutateResult.href) {
    //     router.push(mutateResult.href)
    // }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="mt-5 md:mt-10"
        autoComplete="off"
      >
        <Card className="!px-0 !pt-5 !pb-10  mt-5">
          {steps > 1 && (
            <div className="md:px-14 text-left flex justify-end my-2">
              <CustomButton
                onClick={handleBackBtn}
                className="w-fit bg-transparent text-gray-600 flex flex-row-reverse p-0"
                type="button"
              >
                <ChevronsLeft size={20} /> بازگشت
              </CustomButton>

            </div>
          )}
          <InquiryHeader
            icon={
              <div className="w-[50px] h-[50px] flex justify-center items-center">
                <DisplayServiceIcon
                  containerWidth={33}
                  containerHeight={33}
                  variant="green"
                  status="ACTIVE"
                  border={false}
                  shadow={false}
                >
                  <InquiryInsuranceIcon width={25} height={25} />
                </DisplayServiceIcon>
              </div>
            }
            title={`استعلام بیمه شخص ثالث ${vehicleType}`}
          />

          <div className="px-5 mt-2 md:px-14 space-y-5">
            {steps == 1 ? (
              <>
                <FormField
                  control={form.control}
                  name="plateNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        {isMotor ? (
                          <PlateInputMotor {...field} ref={plateInputRef} />
                        ) : (
                          <PlateInputCar {...field} ref={plateInputRef} />
                        )}
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
                 <div>
                    <FormField
                      control={form.control}
                      name="nationalCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#596068] text-xs">{`کد ملی مالک ${vehicleType}`}</FormLabel>
                          <FormControl>
                            <CustomInput
                              variant="secondary"
                              allowOnlyNumbers
                              maxLength={NATIONAL_CODE_MAX_LENGTH}
                              placeholder="کد ملی مالک را وارد کنید"
                              {...field}
                              ref={nationalCodeRef}
                              leftIcon={<IdCardIcon height={20} width={20} />}
                              direction="rtl"
                              inputMode="numeric"
                              autoFocus
                            />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />
                    {errors.nationalCode && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.nationalCode}
                      </p>
                    )}
                  </div>
                  <div className="mt-4">
                    <FormField
                      control={form.control}
                      name="phoneNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#596068] text-xs">{`شماره موبایل مالک ${vehicleType}`}</FormLabel>
                          <FormControl>
                            <CustomInput
                              variant="secondary"
                              allowOnlyNumbers
                              maxLength={PHONE_NUMBER_MAX_LENGTH}
                              leftIcon={
                                <BookText
                                  width={20}
                                  height={20}
                                  className="text-gray-400"
                                />
                              }
                              placeholder="شماره موبایل مالک را وارد کنید"
                              {...field}
                              ref={phoneNumberRef}
                              inputMode="numeric"
                              direction="rtl"
                            />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />
                  </div>
              </>
            ) : (
              ""
            )}

            <div className="mt-8">
              {steps == 2 ? (
                <>
                 
                  <div className="mt-4">
                    <FormField
                      control={form.control}
                      name="insuranceNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#596068] text-xs">{`شماره یکتای بیمه ${vehicleType}`}</FormLabel>
                          <FormControl>
                            <CustomInput
                              variant="secondary"
                              allowOnlyNumbers
                              // maxLength={PHONE_NUMBER_MAX_LENGTH}
                              leftIcon={
                                <BookText
                                  width={20}
                                  height={20}
                                  className="text-gray-400"
                                />
                              }
                              placeholder="شماره یکتای بیمه را وارد کنید"
                              {...field}
                              ref={insuranceNumberRef}
                              inputMode="numeric"
                              direction="rtl"
                            />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />
                    {errors.insuranceNumber && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.insuranceNumber}
                      </p>
                    )}
                  </div>
                  
                </>
              ) : (
                ""
              )}
              <div className="mt-5">
                <ChoiceWrapper backgroundColor="#FFF5D8" borderColor="#F7BC06">
                  <div className="flex py-3 flex-col gap-y-1">
                    <p className="text-[#5E646B] text-sm">
                      هزینه استعلام: 16,170 تومان
                    </p>
                    <p className="text-[#5E646B] text-sm">
                      خودراکس هیچ دخل و تصرفی در تعیین این هزینه ندارد
                    </p>
                  </div>
                </ChoiceWrapper>
              </div>

              {steps == 1 ? (
                <CustomButton onClick={handleFirstStep} className="mt-5 !py-5">
                  تایید پلاک
                </CustomButton>
              ) : (
                <CustomButton
                  loading={isLoading}
                  // disabled={
                  //   isLoading ||
                  //   status === "DISABLED" ||
                  //   status === "COMING_SOON"
                  // }
                  // type="submit"
                  onClick={onSubmit}
                  className="mt-5 !py-5"
                >
                  استعلام
                </CustomButton>
              )}

              {(status === "DISABLED" || status === "COMING_SOON") && (
                <div className="mt-5">
                  <StatusMessage status={status} />
                </div>
              )}
            </div>
          </div>
        </Card>
      </form>
    </Form>
  );
}
