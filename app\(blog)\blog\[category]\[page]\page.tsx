import { apiClient } from "@/lib/apiClient"
import { Metada<PERSON> } from "next"
import { CateoryResponse } from "@/lib/types/article.types"
import BlogMainContent from "@/components/blog/main/BlogMainContent"
import { getBlogPosts } from "@/actions/blog.action"
import { notFound } from "next/navigation"

export const metadata: Metadata = {
  // robots: 'noindex, nofollow',
};

type PageProps = {
  params: Promise<{ category: string; page: string }>
}

/**
 * Category pagination page that handles:
 * - /blog/category-slug/2, /blog/category-slug/3, etc.
 */
const CategoryPaginationPage = async ({ params }: PageProps) => {
  const { category: rawCategory, page: pageParam } = await params;
  
  // Decode the category slug
  const category = decodeURIComponent(rawCategory);
  
  // Parse page number
  const page = parseInt(pageParam);
  
  // If page is not a valid number or less than 2, redirect
  if (isNaN(page) || page < 2) {
    notFound();
  }
  
  try {
    // Fetch categories and articles
    const CategoriesResponse: CateoryResponse = await apiClient("categories")
      .then(res => res.json());
    
    const articlesResponse = await getBlogPosts(page, category);
    
    if (!articlesResponse.success) {
      notFound();
    }
    
    const ArticlesResponse = articlesResponse.data;
    
    // Check if page exists and has articles
    if (!ArticlesResponse.data || ArticlesResponse.data.length === 0) {
      notFound();
    }
    
    return (
      <BlogMainContent 
        ArticlesResponse={ArticlesResponse.data || []} 
        CategoriesResponse={CategoriesResponse}
        currentPage={ArticlesResponse.meta?.current_page || page}
        lastPage={ArticlesResponse.meta?.last_page || 1}
        category={category}
      />
    );
    
  } catch (error) {
    console.error("Error fetching category blog data:", error);
    notFound();
  }
}

export default CategoryPaginationPage
