export interface IconProps {
    className?: string
}

export type ViolationQueryParams = {
    left: string,
    middle?: string,
    right: string,
    alphabet?: string,
    isMotor: 'true' | 'false',
    withDetails: 'true' | 'false'
    phoneNumber?: string,
    nationalCode?: string,
    inquiry?: 'true' | 'false',
    reInquiry?: 'false' | 'true',
    message?: string
    type?: string
    license_number?: string
    national_id?: string
    mobile_number?: string
    unique_insurance_policy?: string
}


export interface EnvConfig {
    BASE_URL: string;
    BASE_URL_2: string;
    NODE_ENV: "development" | "production" | "test";
    X_APPLICATION_TOKEN: string;
    Services: {
        VEHICLE_VIOLATION_SECTION: ServiceStatusType;
        MOTORCYCLE_VIOLATION_SECTION: ServiceStatusType;
        DRIVING_VIOLATION_IMAGE_SECTION: ServiceStatusType;
        THIRD_PARTY_INSURANCE_INQUIRY_SECTION: ServiceStatusType;
        LICENSE_DRIVER_STATUS_SECTION: ServiceStatusType;
        VEHICLE_CARD_AND_DOCUMENT_STATUS_SECTION: ServiceStatusType;
        NEGATIVE_SCORE_INQUIRY_SECTION: ServiceStatusType;
        LICENSE_PLATE_HISTORY_INQUIRY_SECTION: ServiceStatusType;
        FREEWAY_TOLL_SECTION: ServiceStatusType;
        BUYING_BODY_INSURANCE_SECTION: ServiceStatusType;
    };
}


// TODO: this interface is duplicated and must fix
export interface PageContentFAQ {
  question: string;
  answer: string;
}

export interface PageContentCategory {
  title: string | null;
  slug: string | null;
}

export interface PageContentResponse {
  id: string
  title: string;
  meta_title: string;
  meta_description: string;
  description: string;
  schema: string;
  tags: string[];
  faqs: PageContentFAQ[];
  meta_search: string | null;
  canonical: string | null;
  comments: any[]; 
  comments_count: number;
  category: PageContentCategory;
  date_ago: string;
  author_fullname: string;
}

export interface FaqItem {
    question: string;
    answer: string;
}

// export type ServiceStatusType = 'DEACTIVE' | 'ACTIVE'
export type ServiceStatusType = string
export type ServiceColorVariantType = 'blue' | 'green' | 'emerald' | 'purple' | 'yellow' | 'indigo' | 'lime' | 'red';


// articles types
export interface Article {
    title: string;
    slug: string;
    summary: string;
    comments_count: number;
    author_fullname: string;
    author_avatar: string | null;
    study_time: string;
    cover: string;
    date: string;
}

export interface Links {
    first: string;
    last: string | null;
    prev: string | null;
    next: string | null;
}

export interface Meta {
    current_page: number;
    from: number;
    path: string;
    per_page: number;
    to: number;
}

export interface ArticlesApiResponse {
    data: Article[];
    links: Links;
    meta: Meta;
    success: boolean;
    status: number;
}

export interface UserAddressesResponse {
    success: boolean;
    message: string;
    data: UserAddress[];
    status: number;
}

export interface UserAddress {
    id: string;
    name: string;
    receiver_name: string;
    receiver_phone: string;
    is_recipient_self: boolean;
    province: string;
    city: string;
    zip_code: number;
    address: string;
    latitude: number;
    longitude: number;
}


export interface UserProfileData {
    full_name?: string
    email?: string
    phone?: string
    national_code?: string
    shamsi_birth_date?: string
    profile_image?: string
}

export interface DeliveryMethod {
  id: string;
  title: string;
  price: number;
  image_url: string | null;
}

export interface DeliveryMethodsResponse {
  success: boolean;
  message: string;
  data: DeliveryMethod[];
  status: number;
}
