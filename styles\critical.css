/* Critical CSS - Above the fold styles only */

@tailwind base;

/* Only essential base styles */
* {
    direction: rtl;
}

html {
    scroll-behavior: smooth;
    overscroll-behavior-y: none;
}

body {
    font-family: var(--font-iransans), Ta<PERSON>a, <PERSON>l, sans-serif;
    background-color: #F5F6F8;
    color: #62676E;
}

/* Essential layout utilities */
.flex { display: flex; }
.block { display: block; }
.hidden { display: none; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

/* Essential spacing */
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-5 { margin: 1.25rem; }

/* Essential colors */
.bg-white { background-color: #ffffff; }
.bg-primary { background-color: #1F84FB; }
.text-white { color: #ffffff; }
.text-black { color: #000000; }
.text-gray-500 { color: #6b7280; }

/* Essential sizing */
.w-full { width: 100%; }
.h-full { height: 100%; }
.w-auto { width: auto; }
.h-auto { height: auto; }

/* Essential flexbox */
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.flex-col { flex-direction: column; }

/* Essential borders */
.border { border-width: 1px; }
.border-gray-300 { border-color: #d1d5db; }
.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }

/* Essential text */
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.font-bold { font-weight: 700; }
.text-center { text-align: center; }

/* Essential custom classes */
.broken-div {
    clip-path: polygon(0 100%, 100% 100%, 100% 0, 50% 20%, 0 0);
}

.borderless-input {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

.left-direction {
    direction: ltr !important;
}

.login-form {
    background-image: url("/assets/images/fade-logo.png");
    background-repeat: no-repeat;
    background-position-x: 20px;
    background-position-y: 90px;
}

/* Essential responsive */
@media (min-width: 768px) {
    .md\\:flex { display: flex; }
    .md\\:hidden { display: none; }
    .md\\:block { display: block; }
    .md\\:p-4 { padding: 1rem; }
    .md\\:text-lg { font-size: 1.125rem; }
}

@media (min-width: 1024px) {
    .lg\\:flex { display: flex; }
    .lg\\:hidden { display: none; }
    .lg\\:block { display: block; }
    .lg\\:p-6 { padding: 1.5rem; }
    .lg\\:text-xl { font-size: 1.25rem; }
}

/* CSS Variables for shadcn/ui */
:root {
    --background: #F5F6F8;
    --foreground: #62676E;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: #1F84FB;
    --primary-foreground: #0A0A0C;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: #F9FAFB;
    --muted-foreground: #9DA5B0;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: #15192A;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: #E4E6E9;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --radius: 0.5rem;
}
