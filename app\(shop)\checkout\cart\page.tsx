// import BasketCart from '@/components/shop/checkout/BasketCart'
// import DiscountCode from '@/components/shop/checkout/DiscountCode'
// import FactorCard from '@/components/shop/checkout/FactorCard'
// import PaymentMethodCard from '@/components/shop/checkout/PaymentMethodCard'
// import OrderSummary from '@/components/shop/checkout/OrderSummary'
// import UserAddresses from '@/components/shop/checkout/UserAddresses'
// import { getUserCart } from '@/actions/cart.action'
import CartPageClient from '@/components/shop/checkout/CartPageClient'
import { ArrowRight, MoveRight } from 'lucide-react'



const CheckoutCartPage = async () => {


  return (
    <>
      <main className='container mx-auto pb-[64px] mb-16 '>
        <div className='px-1 mt-8 flex items-center gap-2' >
         <button type='button' className='bg-gray-200/50 cursor-not-allowed opacity-50 max-md:hidden rounded-lg px-4 py-1.5 flex items-center gap-3'>
           <ArrowRight className=' max-md:hidden' size={26} /> بازگشت
         </button>
        </div>


        <CartPageClient />


      </main>
    </>

  )
}

export default CheckoutCartPage