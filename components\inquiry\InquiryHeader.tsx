import {ReactNode} from "react";

type Props = {
    title: string
    icon?: ReactNode
}

export default function InquiryHeader({title, icon}: Props) {
    return (
        <div className='relative h-[50px] flex items-center gap-x-2'>
            <div
                className='h-full w-[50px] md:w-[90px]  rounded-tl-full rounded-bl-full bg-gradient-to-r from-[#F5F6F8] to-transparent'></div>
            <h2 className='text-[#212121] md:text-lg text-base'>{title}</h2>
            <div className='absolute top-[1px] right-[5px] md:right-[40px]'>{icon}
            </div>
        </div>
    );
}
