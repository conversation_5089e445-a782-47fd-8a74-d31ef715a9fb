/**
 * Simple PWA Initializer Component
 * Handles service worker registration and PWA setup
 */

'use client';

import { useEffect } from 'react';
import { initializePWA, setupPWAInstallPrompt } from '@/lib/utils/pwa-utils';

/**
 * Simple PWA initialization component
 */
export default function PWAInitializer() {
  useEffect(() => {
    const initPWA = async () => {
      try {
        await initializePWA();
        setupPWAInstallPrompt();
        console.log('[PWA] Initialization completed');
      } catch (error) {
        console.error('[PWA] Initialization failed:', error);
      }
    };

    initPWA();
  }, []);

  return null;
}

/**
 * PWA Install Button Component
 * Shows when app can be installed as PWA
 */
export function PWAInstallButton() {
  return (
    <button
      id="pwa-install-button"
      style={{ display: 'none' }}
      className="fixed bottom-4 left-4 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg shadow-lg transition-colors duration-200 z-50"
    >
      نصب اپلیکیشن
    </button>
  );
}
