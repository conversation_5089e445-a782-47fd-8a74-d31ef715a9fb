/**
 * Simple Network Status Indicator Component
 * Shows network connectivity status to users
 */

'use client';

import { useEffect, useState } from 'react';
import { useNetworkStatus } from '@/lib/hooks/useNetworkStatus';
import { isNetworkRequiredPage } from '@/lib/utils/pwa-utils';
import { usePathname } from 'next/navigation';

interface NetworkStatusIndicatorProps {
  className?: string;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

/**
 * Simple network status indicator
 */
export default function NetworkStatusIndicator({
  className = '',
  position = 'top-right'
}: NetworkStatusIndicatorProps) {
  const { isOnline, isOffline, effectiveType } = useNetworkStatus();
  const pathname = usePathname();
  const [showIndicator, setShowIndicator] = useState(false);

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4'
  };

  useEffect(() => {
    const shouldShow = isOffline || isNetworkRequiredPage(pathname);
    setShowIndicator(shouldShow);
  }, [isOffline, pathname]);

  if (!showIndicator) {
    return null;
  }

  return (
    <div className={`fixed z-50 ${positionClasses[position]} ${className}`}>
      <div
        className={`
          flex items-center gap-2 px-3 py-2 rounded-lg shadow-lg
          ${isOnline
            ? 'bg-green-500 text-white'
            : 'bg-red-500 text-white'
          }
        `}
      >
        <div className={`w-3 h-3 rounded-full ${isOnline ? 'bg-white' : 'bg-white'}`} />
        <span className="text-sm font-medium">
          {isOnline ? 'آنلاین' : 'آفلاین'}
        </span>
        {isOnline && effectiveType && (
          <span className="text-xs">
            ({getConnectionQualityText(effectiveType)})
          </span>
        )}
      </div>

      {isOffline && isNetworkRequiredPage(pathname) && (
        <div className="mt-2">
          <button
            onClick={() => window.location.href = '/offline'}
            className="w-full bg-white text-gray-800 text-xs font-medium py-2 px-3 rounded-lg"
          >
            مشاهده صفحه آفلاین
          </button>
        </div>
      )}
    </div>
  );
}

/**
 * Get Persian text for connection quality
 */
function getConnectionQualityText(effectiveType: string): string {
  switch (effectiveType) {
    case 'slow-2g':
      return 'خیلی کند';
    case '2g':
      return 'کند';
    case '3g':
      return 'متوسط';
    case '4g':
      return 'سریع';
    default:
      return 'نامشخص';
  }
}
