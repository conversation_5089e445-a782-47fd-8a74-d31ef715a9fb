/**
 * Article response interfaces for the blog section
 */

import { Article } from "./types";


export interface ArticleAd {
  cover: string | null;
  description: string | null;
  link: string | null;
  title: string | null;
}


export interface ArticleComment {
  id: string;
  author: string;
  content: string;
  date: string;

}


export interface ArticleResponse {
  data: {
    ads: ArticleAd;
    article_default: ArticleDefault
    author_about: string;
    author_avatar: string | null;
    author_fullname: string;
    category: CategoryItem;
    comments: ArticleComment[];
    comments_count: number;
    canonical: string
    cover: string | null;
    date_ago: string;
    description: string;
    faqs: any | null;
    meta_description: string | null;
    meta_title: string | null;
    meta_search: string
    schema: any | null;
    sidebar_right: boolean;
    tags: string[] | null;
    title: string;
    type: "article" | "category"
    articles?: Article[]
    title_article: string
    slug: string
    id: string
    random_articles: RandomArticle[]

  };
  status: number;
  success: boolean;
}
export interface RandomArticle {
  cover: string
  slug: string
  title: string
}
export interface ArticleDefault {
  cover: string
  slug: string
  title: string
}


export interface ArticleListItem {
  id: string;
  title: string;
  slug: string;
  summary: string;
  cover: string | null;
  date_ago: string;
  author_fullname: string;
  author_avatar: string | null;
  comments_count: number;
  category: CategoryItem;
}
export interface CategoryItem {
  title: string
  slug: string
  total_articles: number
}
export interface CateoryResponse {
  data: CategoryItem[];
  status: number;
  success: boolean;
}

export interface ArticleListResponse {
  data: ArticleListItem[];
  status: number;
  success: boolean;
  pagination?: {
    current_page: number;
    total_pages: number;
    total_items: number;
    per_page: number;
  };
}
