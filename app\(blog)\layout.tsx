import ShopNavbar from "@/components/Header/ShopNavbar";
import Footer from "@/components/UI/Footer";
import type{ ReactNode } from "react";
import "@/styles/styles.css"
import Navbar from "@/components/Header/Navbar";


type Props = {
    children: ReactNode;
}

export default function layout({children}: Props) {
    return (
        <>
            <ShopNavbar />
            <div className="md:hidden">
                <Navbar />
            </div>
            {/* <ShopHeader /> */}
            {children}
            <Footer />            
        </>
    );
}
