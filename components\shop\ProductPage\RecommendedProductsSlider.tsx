'use client';

import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import Product from '../mainPage/Product'; // adjust path as needed
import { useEffect, useRef, useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { SimilarProduct } from '@/lib/types/product.types';
import { getSimilarProductsAction } from '@/actions/product.action';
// import type { Swiper as  SwiperNavigation } from 'swiper/types';
const products = Array(7).fill({}); // Replace with real data
export default function RecommendedProductsSlider({ slug }: { slug: string }) {
  const prevRef = useRef<HTMLButtonElement>(null);
  const nextRef = useRef<HTMLButtonElement>(null);
  const [swiperInitialized, setSwiperInitialized] = useState(false);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const [recommendedProducts, setRecommendedProducts] = useState<SimilarProduct[]>([]);

  useEffect(() => {
    const getSimilars = async () => {
      const response = await getSimilarProductsAction(slug);
      setRecommendedProducts(response.data);
    }
    getSimilars()
  }, [])
  // console.log(recommendedProducts);




  return (
    <section className="w-full py-6">
      <div className="flex items-center justify-between px-4 mb-4">
        <h2 className="text-lg font-semibold text-gray-700">محصولات پیشنهادی ما</h2>
      </div>

      <div dir="rtl" className="relative px-4 md:min-h-[400px]" >
        {/* Navigation Buttons */}
        <div className="flex gap-2 justify-end mb-5">
          <button
            ref={prevRef}
            className={`w-8 h-8 rounded-full bg-white border border-gray-200 shadow transition-all flex items-center justify-center ${isBeginning ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'
              }`}
            disabled={isBeginning}
          >
            <ChevronRight className="w-4 h-4 text-gray-500" />
          </button>
          <button
            ref={nextRef}
            className={`w-8 h-8 rounded-full bg-white border border-gray-200 shadow transition-all flex items-center justify-center ${isEnd ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'
              }`}
            disabled={isEnd}
          >
            <ChevronLeft className="w-4 h-4 text-gray-500" />
          </button>
        </div>

        {!swiperInitialized && (
          <div className="flex gap-4 animate-pulse">
            {products.map((_, i) => (
              <div key={i} className="w-[280px] h-[300px] bg-gray-100 rounded-lg" />
            ))}
          </div>
        )}

        <Swiper
          modules={[Navigation]}
          navigation={{
            prevEl: prevRef.current,
            nextEl: nextRef.current,
          }}
          onBeforeInit={(swiper) => {
            swiper.params.navigation = swiper.params.navigation || {};
            Object.assign(swiper.params.navigation, {
              prevEl: prevRef.current,
              nextEl: nextRef.current,
            });
          }}
          onInit={(swiper) => {
            setSwiperInitialized(true);
            swiper.el.classList.add('swiper-initialized');
            swiper.update();
            setIsBeginning(swiper.isBeginning);
            setIsEnd(swiper.isEnd);
          }}
          onSlideChange={(swiper) => {
            setIsBeginning(swiper.isBeginning);
            setIsEnd(swiper.isEnd);
          }}
          spaceBetween={16}
          slidesPerView={1.2}
          observer={true}
          observeParents={true}
          breakpoints={{
            640: { slidesPerView: 2.2 },
            768: { slidesPerView: 3 },
            1024: { slidesPerView: 4 },
          }}
          className="swiper-container"
        >
          {recommendedProducts.length > 0 && recommendedProducts.map((product, index) => (
            <SwiperSlide key={index}>
              <Product product={product} width="full" />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </section>
  );
}