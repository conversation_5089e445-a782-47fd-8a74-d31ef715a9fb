# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

.idea

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

/.github

# PWA files
/public/sw.js
/public/sw.js.map
/public/workbox-*.js
/public/workbox-*.js.map
/public/fallback-*.js
/public/precache-manifest.*.js
/public/sw-*.js

# PWA cache and runtime files
sw-precache-config.js
workbox-config.js

# Service worker generated files
**/sw.js
**/custom-sw.js
**/sw.js.map
**/workbox-*.js
**/workbox-*.js.map

# PWA build artifacts
.pwa/
pwa-build/