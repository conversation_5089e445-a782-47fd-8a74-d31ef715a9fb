/**
 * Example Network-Aware Component
 * Demonstrates how to use network status in your components
 * Shows different UI states based on connectivity
 */

'use client';

import { useState } from 'react';
import { useNetworkStatus } from '@/lib/hooks/useNetworkStatus';

/**
 * Example component that adapts to network status
 * @returns JSX.Element - Network-aware component
 */
export default function NetworkAwareComponent() {
  const { 
    isOnline, 
    isOffline, 
    effectiveType, 
    testConnectivity,
    isNetworkDependentPage 
  } = useNetworkStatus();
  
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [lastTestResult, setLastTestResult] = useState<boolean | null>(null);

  /**
   * Handle connectivity test
   */
  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    try {
      const result = await testConnectivity();
      setLastTestResult(result);
    } catch (error) {
      setLastTestResult(false);
    } finally {
      setIsTestingConnection(false);
    }
  };

  /**
   * Handle form submission with network check
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isOffline) {
      alert('این عملیات نیاز به اتصال اینترنت دارد');
      return;
    }

    // Proceed with form submission
    console.log('Form submitted while online');
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-4 text-gray-800">
        مثال کامپوننت هوشمند شبکه
      </h2>

      {/* Network Status Display */}
      <div className="mb-6">
        <div className={`
          flex items-center gap-2 p-3 rounded-lg
          ${isOnline ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}
        `}>
          <div className={`
            w-3 h-3 rounded-full
            ${isOnline ? 'bg-green-500' : 'bg-red-500'}
          `} />
          <span className="font-medium">
            وضعیت: {isOnline ? 'آنلاین' : 'آفلاین'}
          </span>
        </div>

        {/* Connection Quality */}
        {isOnline && effectiveType && (
          <div className="mt-2 text-sm text-gray-600">
            کیفیت اتصال: {getConnectionQualityText(effectiveType)}
          </div>
        )}

        {/* Page Type Warning */}
        {isNetworkDependentPage() && (
          <div className="mt-2 p-2 bg-orange-50 text-orange-700 text-sm rounded">
            ⚠️ این صفحه نیاز به اتصال اینترنت دارد
          </div>
        )}
      </div>

      {/* Connection Test */}
      <div className="mb-6">
        <button
          onClick={handleTestConnection}
          disabled={isTestingConnection}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
        >
          {isTestingConnection ? 'در حال تست...' : 'تست اتصال'}
        </button>

        {lastTestResult !== null && (
          <div className={`
            mt-2 p-2 rounded text-sm
            ${lastTestResult 
              ? 'bg-green-50 text-green-700' 
              : 'bg-red-50 text-red-700'
            }
          `}>
            نتیجه تست: {lastTestResult ? 'موفق' : 'ناموفق'}
          </div>
        )}
      </div>

      {/* Example Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            نام کاربری
          </label>
          <input
            type="text"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="نام کاربری خود را وارد کنید"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            ایمیل
          </label>
          <input
            type="email"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="ایمیل خود را وارد کنید"
          />
        </div>

        <button
          type="submit"
          disabled={isOffline}
          className={`
            w-full font-medium py-2 px-4 rounded-lg transition-colors duration-200
            ${isOffline 
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
              : 'bg-green-600 hover:bg-green-700 text-white'
            }
          `}
        >
          {isOffline ? 'نیاز به اتصال اینترنت' : 'ارسال'}
        </button>
      </form>

      {/* Offline Message */}
      {isOffline && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">
            شما در حال حاضر آفلاین هستید. برخی از امکانات ممکن است در دسترس نباشند.
          </p>
        </div>
      )}

      {/* Network-dependent Features */}
      <div className="mt-6 space-y-2">
        <h3 className="font-medium text-gray-800">امکانات:</h3>
        
        <div className="space-y-1 text-sm">
          <div className={`flex items-center gap-2 ${isOnline ? 'text-green-600' : 'text-gray-400'}`}>
            <span>{isOnline ? '✅' : '❌'}</span>
            <span>ارسال فرم</span>
          </div>
          
          <div className={`flex items-center gap-2 ${isOnline ? 'text-green-600' : 'text-gray-400'}`}>
            <span>{isOnline ? '✅' : '❌'}</span>
            <span>بروزرسانی داده‌ها</span>
          </div>
          
          <div className="flex items-center gap-2 text-green-600">
            <span>✅</span>
            <span>مطالعه محتوای ذخیره شده</span>
          </div>
          
          <div className="flex items-center gap-2 text-green-600">
            <span>✅</span>
            <span>مشاهده صفحات بازدید شده</span>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Get Persian text for connection quality
 */
function getConnectionQualityText(effectiveType: string): string {
  switch (effectiveType) {
    case 'slow-2g':
      return 'خیلی کند';
    case '2g':
      return 'کند';
    case '3g':
      return 'متوسط';
    case '4g':
      return 'سریع';
    default:
      return 'نامشخص';
  }
}
