'use client';

import { useRouter } from 'next/navigation';
import Pagination from '@/components/common/Pagination';

interface BlogPaginationProps {
  currentPage: number;
  lastPage: number;
  category?: string;
}

/**
 * Blog pagination component that handles URL generation for different blog page types
 * Supports:
 * - Main blog pagination: /blog -> /blog/2
 * - Category pagination: /blog/category -> /blog/category/2
 */
const BlogPagination = ({ currentPage, lastPage, category }: BlogPaginationProps) => {
  const router = useRouter();

  const handlePageChange = (page: number) => {
    let url: string;
    
    if (page === 1) {
      // For page 1, use clean URLs
      if (category) {
        url = `/blog/${category}`;
      } else {
        url = '/blog';
      }
    } else {
      // For other pages, append page number
      if (category) {
        url = `/blog/${category}/${page}`;
      } else {
        url = `/blog/${page}`;
      }
    }
    
    router.push(url);
  };

  return (
    <Pagination
      currentPage={currentPage}
      lastPage={lastPage}
      onPageChange={handlePageChange}
    />
  );
};

export default BlogPagination;
