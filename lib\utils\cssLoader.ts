/**
 * Dynamic CSS loader utility
 * Loads CSS only when needed to reduce unused CSS
 */

let loadedStyles = new Set<string>();

/**
 * Dynamically load CSS for Swiper components
 */
export const loadSwiperCSS = async () => {
  if (loadedStyles.has('swiper')) return;

  try {
    // CSS is now loaded via minimal-swiper.css imports
    loadedStyles.add('swiper');
  } catch (error) {
    console.warn('Failed to load Swiper CSS:', error);
  }
};

/**
 * Dynamically load CSS for Leaflet components
 */
export const loadLeafletCSS = async () => {
  if (loadedStyles.has('leaflet')) return;

  try {
    // CSS is now loaded via minimal-leaflet.css imports
    loadedStyles.add('leaflet');
  } catch (error) {
    console.warn('Failed to load Leaflet CSS:', error);
  }
};

/**
 * Preload critical CSS for better performance
 */
export const preloadCriticalCSS = () => {
  // This can be called on app initialization for critical components
  if (typeof window !== 'undefined') {
    // Preload Swiper CSS if sliders are likely to be used
    const hasSliders = document.querySelector('[class*="swiper"]');
    if (hasSliders) {
      loadSwiperCSS();
    }
    
    // Preload Leaflet CSS if maps are likely to be used
    const hasMaps = document.querySelector('[class*="leaflet"]');
    if (hasMaps) {
      loadLeafletCSS();
    }
  }
};

/**
 * Reset loaded styles (useful for testing)
 */
export const resetLoadedStyles = () => {
  loadedStyles.clear();
};
