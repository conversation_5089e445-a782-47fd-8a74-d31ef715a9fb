'use client'

import { useEffect, useState } from "react";
import {
    NATIONAL_CODE_MAX_LENGTH, PHONE_NUMBER_MAX_LENGTH
} from "@/lib/constants";
import Card from "@/components/common/Card";
import InquiryHeader from "@/components/inquiry/InquiryHeader";
import ChoiceWrapper from "@/components/common/ChoiceWrapper";
import CustomButton from "@/components/UI/CustomButton";
import { Captions, IdCard, Smartphone } from "lucide-react";
import CircleMinusRedIcon from "@/components/common/svg/CircleMinusRedIcon";
import { ServiceStatusType } from "@/lib/types/types";
import { usePathname } from "next/navigation";
import { getDrivingLicensePoint } from "@/actions/inquiry.action";
import { useRouter } from "nextjs-toploader/app";
import toast from "react-hot-toast";
type Props = {
    status?: ServiceStatusType;
}

interface FormData {
    license_number: string;
    national_id: string;
    mobile_number: string;
}

interface FormErrors {
    license_number?: string;
    national_id?: string;
    mobile_number?: string;
}

export function DrivingLicensePointForm({ status }: Props) {
    const path = usePathname();
    const router = useRouter()
    const [showMoreFields, setShowMoreFields] = useState(false);
    const [formData, setFormData] = useState<FormData>({
        license_number: "",
        national_id: "",
        mobile_number: "",
    });
    const [errors, setErrors] = useState<FormErrors>({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [hasTriedConfirmPhone, setHasTriedConfirmPhone] = useState(false);
    const [hasTriedSubmit, setHasTriedSubmit] = useState(false);


    /**
     * Validates phone number format
     */
    const validateMobileNumber = (mobile: string): string | undefined => {
        if (!mobile) return "شماره موبایل الزامی است";
        if (mobile.length !== 11) return "شماره موبایل باید 11 رقم باشد";
        if (!mobile.startsWith('09')) return "شماره موبایل باید با 09 شروع شود";
        return undefined;
    };

    const validateNationalId = (nationalId: string): string | undefined => {
        if (!nationalId) return "کد ملی الزامی است";
        if (nationalId.length !== 10) return "کد ملی باید 10 رقم باشد";
        return undefined;
    };


    const validateLicenseNumber = (licenseNumber: string): string | undefined => {
        if (!licenseNumber) return "شماره گواهینامه الزامی است";
        if (licenseNumber.length < 8) return "شماره گواهینامه نامعتبر است";
        return undefined;
    };


    const handleInputChange = (field: keyof FormData, inputValue: string) => {

        const onlyNumbers = inputValue.replace(/[^0-9]/g, "")

        setFormData(previousFormData => ({
            ...previousFormData,
            [field]: onlyNumbers
        }))

        // if there is any error, remove it
        const hasError = Boolean(errors[field]);
        if (hasError) {
            setErrors(previousErrors => ({
                ...previousErrors,
                [field]: undefined
            }))
        }
    }



    const handleConfirmLicenseNumber = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setHasTriedConfirmPhone(true);

        const licenseError = validateLicenseNumber(formData.license_number);
        if (licenseError) {
            setErrors({ license_number: licenseError });
            return;
        }
        setShowMoreFields(true);
    };


    /**
     * Validates all form fields
     */
    const validateForm = (): boolean => {
        const newErrors: FormErrors = {};

        const mobileError = validateMobileNumber(formData.mobile_number);
        if (mobileError) newErrors.mobile_number = mobileError;

        if (showMoreFields) {
            const nationalIdError = validateNationalId(formData.national_id);
            if (nationalIdError) newErrors.national_id = nationalIdError;

            const licenseError = validateLicenseNumber(formData.license_number);
            if (licenseError) newErrors.license_number = licenseError;
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };


    const handleSubmit = async (e: React.FormEvent) => {

        e.preventDefault();

        // if (status === 'COMING_SOON' || status === "DISABLED") return;
        setHasTriedSubmit(true); // کاربر روی دکمه استعلام زده

        if (!validateForm()) return;

        setIsSubmitting(true);
        try {
            const apiData = {
                national_id: formData.national_id,
                license_number: formData.license_number,
                mobile_number: formData.mobile_number
            };

            const response = await getDrivingLicensePoint(apiData);


            if (response.success) {
                console.log('Driving license point inquiry successful:', response);
                // TODO: Handle successful response (e.g., show results, navigate to results page)
                router.push(`/driving-license-point/result?trace_number=${response.data.traceNumber}`)
            } else if (response.status === 402) {
                // console.error('API returned error:', response.error);
                // TODO: Show error message to user
                localStorage.setItem('walletMessage', response.data.message);
                router.push(`/wallet?license_number=${formData.license_number}&national_id=${formData.national_id}&mobile_number=${formData.mobile_number}&type=license_point`);
            } else if (response.status === 401) {
                router.push(`/login?license_number=${formData.license_number}&national_id=${formData.national_id}&mobile_number=${formData.mobile_number}&type=license_point`)
            } else {
                toast.error(response.data.mesage || "خطایی رخ داده است. لطفا مجددا تلاش کنید.")
            }
        } catch (error) {
            console.error('Submission error:', error);
            // TODO: Show error message to user
        } finally {
            setIsSubmitting(false);
        }
    };
    useEffect(() => {
        console.log(hasTriedSubmit);

    }, [hasTriedSubmit])


    return (
        <form onSubmit={handleSubmit} className="mt-5 md:mt-10" autoComplete="off">
            <Card className='!px-0 !pt-5 !pb-10 mt-5'>
                {path === "/driving-license-point" ? (
                    <InquiryHeader

                        icon={
                            <div className='bg-transparent  w-[50px] h-[50px] flex justify-center items-center'>
                                <CircleMinusRedIcon height={35} width={35} />
                            </div>
                        }
                        title="استعلام نمره منفی"
                    />
                ) : (
                    <InquiryHeader
                        icon={
                            <div className='bg-transparent w-[50px] h-[50px] flex justify-center items-center'>
                                <IdCard color="#7495D1" height={35} width={35} />
                            </div>
                        }
                        title="استعلام وضعیت گواهینامه"
                    />
                )}

                <div className='px-5 mt-2 md:px-14'>
                    <div className='mt-2'>

                        <div className='mt-4'>
                            <label className='text-[#596068]  block mb-2'>
                                برای استعلام نمره منفی، شماره گواهینامه را وارد کنید.
                            </label>
                            <div className="relative flex items-center w-full">
                                <div className="absolute left-5">
                                    <Captions height={32} width={32} className='text-gray-400' />
                                </div>
                                <input
                                    type="text"
                                    inputMode="numeric"
                                    // dir="rtl"
                                    maxLength={PHONE_NUMBER_MAX_LENGTH}
                                    placeholder="شماره گواهینامه"
                                    value={formData.license_number}
                                    onChange={(e) => handleInputChange('license_number', e.target.value)}
                                    className="w-full px-5 text-center pl-14 h-[65px] text-sm md:text-base py-2 border border-gray-300 bg-muted text-primary-foreground rounded-2xl  placeholder:text-sm placeholder:muted-foreground focus:ring-gray-400"
                                    dir="rtl"
                                    style={{ textAlign: 'center', unicodeBidi: 'plaintext' }}
                                />
                            </div>
                            {hasTriedConfirmPhone && errors.license_number && (
                                <p className="text-red-500 mt-3">{errors.license_number}</p>
                            )}
                        </div>
                        {
                            showMoreFields ?
                                <>
                                    <div className="mt-5">
                                        <label className='text-[#596068] block mb-2'>
                                            کد ملی
                                        </label>
                                        <div className="relative flex items-center w-full">
                                            <div className="absolute left-5">
                                                <IdCard width={26} height={26} className="text-gray-400" />
                                            </div>
                                            <input
                                                type="text"
                                                inputMode="numeric"
                                                dir="rtl"
                                                maxLength={NATIONAL_CODE_MAX_LENGTH}
                                                placeholder="کدملی صاحب گواهینامه"
                                                value={formData.national_id}
                                                onChange={(e) => handleInputChange('national_id', e.target.value)}
                                                autoFocus
                                                className="w-full px-5 pl-14 h-[65px] text-sm md:text-base py-2 border border-gray-300 bg-muted text-primary-foreground rounded-2xl text-center placeholder:text-sm placeholder:muted-foreground focus:ring-gray-400"
                                                style={{ textAlign: 'center', unicodeBidi: 'plaintext' }}

                                            />
                                        </div>
                                        {hasTriedSubmit && errors.national_id && (
                                            <p className="text-red-500 mt-3">{errors.national_id}</p>
                                        )}
                                    </div>
                                    <div className='mt-4'>
                                        <label className='text-[#596068] block mb-2'>
                                            شماره موبایل
                                        </label>
                                        <div className="relative flex items-center w-full">
                                            <div className="absolute left-5">
                                                <Smartphone width={26} height={26} className="text-gray-400" />
                                            </div>
                                            <input
                                                type="text"
                                                inputMode="numeric"
                                                dir="rtl"
                                                maxLength={PHONE_NUMBER_MAX_LENGTH}
                                                placeholder="شماره موبایل صاحب گواهینامه"
                                                value={formData.mobile_number}
                                                onChange={(e) => handleInputChange('mobile_number', e.target.value)}
                                                className="w-full px-5 pl-14 h-[65px] text-sm md:text-base py-2 border border-gray-300 bg-muted text-primary-foreground rounded-2xl text-center placeholder:text-sm placeholder:muted-foreground focus:ring-gray-400"
                                                style={{ textAlign: 'center', unicodeBidi: 'plaintext' }}

                                            />
                                        </div>
                                        {hasTriedConfirmPhone && errors.mobile_number && (
                                            <p className="text-red-500 mt-3">{errors.mobile_number}</p>
                                        )}
                                    </div>
                                </>
                                :
                                ''
                        }


                        {!showMoreFields ? (
                            <CustomButton
                                type='button'
                                onClick={handleConfirmLicenseNumber}
                                className='mt-5 !py-5'
                            >
                                تایید شماره گواهینامه
                            </CustomButton>
                        ) : (
                            <CustomButton
                                loading={isSubmitting}
                                // disabled={(status === 'COMING_SOON' || status === "DISABLED") || isSubmitting}
                                type='submit'
                                className='mt-5 !py-5'
                            >
                                استعلام نمره منفی
                            </CustomButton>
                        )}

                        <div className='mt-5'>
                            <ChoiceWrapper
                                backgroundColor='#FFF5D8'
                                borderColor='#F7BC06'
                            >
                                <div className='flex py-3 flex-col gap-y-1'>
                                    <p className='text-[#5E646B] text-sm'>هزینه استعلام نمره منفی گواهینامه: 16,170 تومان</p>
                                    <p className='text-[#5E646B] text-sm'>
                                        خودراکس هیچ دخل و تصرفی در تعیین این هزینه ندارد
                                    </p>
                                </div>
                            </ChoiceWrapper>
                        </div>


                    </div>
                </div>
            </Card>
        </form>
    );
}