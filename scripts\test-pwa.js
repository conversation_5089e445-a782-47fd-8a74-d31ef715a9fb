/**
 * PWA Testing Script
 * Validates PWA implementation and caching strategies
 * Run in browser console to test PWA functionality
 */

class PWATester {
  constructor() {
    this.results = [];
    this.cacheName = 'khodrox-v1';
  }

  /**
   * Run all PWA tests
   */
  async runAllTests() {
    console.log('🧪 Starting PWA Tests...\n');
    
    await this.testServiceWorkerRegistration();
    await this.testCacheStorage();
    await this.testNetworkDetection();
    await this.testOfflinePages();
    await this.testManifest();
    
    this.printResults();
  }

  /**
   * Test service worker registration
   */
  async testServiceWorkerRegistration() {
    console.log('📋 Testing Service Worker Registration...');
    
    try {
      if (!('serviceWorker' in navigator)) {
        this.addResult('❌ Service Worker not supported', false);
        return;
      }

      const registrations = await navigator.serviceWorker.getRegistrations();
      const customSW = registrations.find(reg => 
        reg.active && reg.active.scriptURL.includes('custom-sw.js')
      );

      if (customSW) {
        this.addResult('✅ Custom Service Worker registered', true);
        console.log('   SW Scope:', customSW.scope);
        console.log('   SW State:', customSW.active.state);
      } else {
        this.addResult('❌ Custom Service Worker not found', false);
      }
    } catch (error) {
      this.addResult(`❌ SW Registration Error: ${error.message}`, false);
    }
  }

  /**
   * Test cache storage
   */
  async testCacheStorage() {
    console.log('\n📦 Testing Cache Storage...');
    
    try {
      const cacheNames = await caches.keys();
      console.log('   Available Caches:', cacheNames);

      const cache = await caches.open(this.cacheName);
      const cachedRequests = await cache.keys();
      
      console.log(`   Cached Items: ${cachedRequests.length}`);
      
      // Test essential cached items
      const essentialItems = [
        '/',
        '/offline',
        '/manifest.json'
      ];

      for (const item of essentialItems) {
        const cached = await cache.match(item);
        if (cached) {
          this.addResult(`✅ ${item} is cached`, true);
        } else {
          this.addResult(`❌ ${item} not cached`, false);
        }
      }

    } catch (error) {
      this.addResult(`❌ Cache Storage Error: ${error.message}`, false);
    }
  }

  /**
   * Test network detection
   */
  async testNetworkDetection() {
    console.log('\n🌐 Testing Network Detection...');
    
    try {
      // Test navigator.onLine
      const isOnline = navigator.onLine;
      this.addResult(`📡 Navigator Online: ${isOnline}`, true);

      // Test connection API if available
      const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
      if (connection) {
        console.log('   Connection Type:', connection.effectiveType);
        console.log('   Downlink:', connection.downlink);
        this.addResult('✅ Connection API available', true);
      } else {
        this.addResult('⚠️ Connection API not available', true);
      }

      // Test connectivity with a lightweight request
      try {
        const response = await fetch('/favicon.png', { 
          method: 'HEAD',
          cache: 'no-cache'
        });
        this.addResult(`✅ Connectivity test: ${response.ok ? 'Success' : 'Failed'}`, response.ok);
      } catch {
        this.addResult('❌ Connectivity test failed', false);
      }

    } catch (error) {
      this.addResult(`❌ Network Detection Error: ${error.message}`, false);
    }
  }

  /**
   * Test offline pages
   */
  async testOfflinePages() {
    console.log('\n📄 Testing Offline Pages...');
    
    try {
      // Test offline page accessibility
      const offlineResponse = await fetch('/offline');
      if (offlineResponse.ok) {
        this.addResult('✅ Offline page accessible', true);
      } else {
        this.addResult('❌ Offline page not accessible', false);
      }

      // Test network-dependent page patterns
      const networkDependentPatterns = [
        '/dashboard',
        '/checkout',
        '/payment',
        '/car-tickets',
        '/motor-tickets',
        '/inquiry',
        '/wallet',
        '/login'
      ];

      const currentPath = window.location.pathname;
      const isNetworkDependent = networkDependentPatterns.some(pattern => 
        currentPath.includes(pattern)
      );

      this.addResult(`📍 Current page network-dependent: ${isNetworkDependent}`, true);

    } catch (error) {
      this.addResult(`❌ Offline Pages Error: ${error.message}`, false);
    }
  }

  /**
   * Test PWA manifest
   */
  async testManifest() {
    console.log('\n📱 Testing PWA Manifest...');
    
    try {
      const manifestResponse = await fetch('/manifest.json');
      if (manifestResponse.ok) {
        const manifest = await manifestResponse.json();
        
        this.addResult('✅ Manifest accessible', true);
        console.log('   App Name:', manifest.name);
        console.log('   Display Mode:', manifest.display);
        console.log('   Theme Color:', manifest.theme_color);
        console.log('   Icons:', manifest.icons?.length || 0);

        // Check required manifest fields
        const requiredFields = ['name', 'start_url', 'display', 'icons'];
        for (const field of requiredFields) {
          if (manifest[field]) {
            this.addResult(`✅ Manifest has ${field}`, true);
          } else {
            this.addResult(`❌ Manifest missing ${field}`, false);
          }
        }

      } else {
        this.addResult('❌ Manifest not accessible', false);
      }
    } catch (error) {
      this.addResult(`❌ Manifest Error: ${error.message}`, false);
    }
  }

  /**
   * Add test result
   */
  addResult(message, success) {
    this.results.push({ message, success });
    console.log(`   ${message}`);
  }

  /**
   * Print test summary
   */
  printResults() {
    console.log('\n📊 PWA Test Results Summary');
    console.log('================================');
    
    const passed = this.results.filter(r => r.success).length;
    const total = this.results.length;
    
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`❌ Failed: ${total - passed}/${total}`);
    
    if (total - passed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => !r.success)
        .forEach(r => console.log(`   ${r.message}`));
    }

    console.log('\n🎯 PWA Score:', Math.round((passed / total) * 100) + '%');
  }

  /**
   * Test specific caching strategy
   */
  async testCachingStrategy(url, expectedStrategy) {
    console.log(`\n🔍 Testing caching for: ${url}`);
    
    try {
      // Clear any existing cache for this URL
      const cache = await caches.open(this.cacheName);
      await cache.delete(url);

      // First request (should go to network)
      const start1 = performance.now();
      const response1 = await fetch(url);
      const end1 = performance.now();
      
      console.log(`   First request: ${Math.round(end1 - start1)}ms`);

      // Second request (should use cache if strategy allows)
      const start2 = performance.now();
      const response2 = await fetch(url);
      const end2 = performance.now();
      
      console.log(`   Second request: ${Math.round(end2 - start2)}ms`);

      // Check if cached
      const cached = await cache.match(url);
      console.log(`   Cached: ${cached ? 'Yes' : 'No'}`);
      
      return {
        firstRequestTime: end1 - start1,
        secondRequestTime: end2 - start2,
        isCached: !!cached
      };

    } catch (error) {
      console.error(`   Error testing ${url}:`, error);
      return null;
    }
  }
}

// Export for use
if (typeof window !== 'undefined') {
  window.PWATester = PWATester;
  
  // Auto-run if script is loaded directly
  if (document.readyState === 'complete') {
    console.log('🚀 PWA Tester loaded. Run: new PWATester().runAllTests()');
  }
}

// Usage instructions
console.log(`
🧪 PWA Testing Instructions:

1. Basic Test:
   const tester = new PWATester();
   await tester.runAllTests();

2. Test Specific Caching:
   await tester.testCachingStrategy('/blog/some-post', 'cache-first');

3. Manual Tests:
   - Go offline in DevTools
   - Visit different page types
   - Check cache in Application tab
   - Test PWA installation prompt
`);
const tester = new PWATester();
   await tester.runAllTests();