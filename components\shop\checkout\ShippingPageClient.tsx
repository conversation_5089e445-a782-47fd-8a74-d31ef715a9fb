"use client"
import User<PERSON><PERSON><PERSON> from './UserAddresses'
import Factor<PERSON>ard from './FactorCard'
import { UserAddressesResponse, UserAddress, DeliveryMethod } from '@/lib/types/types'
import { useState } from 'react'
import { getUserAddressesAction } from '@/actions/userAddress.action'
import DeliveryMethodsList from './DeliveryMethodsList'

const ShippingPageClient = ({ userAddresses, deliveryMethods }: { userAddresses: UserAddressesResponse, deliveryMethods: DeliveryMethod[] }) => {
    const [selectedAddress, setSelectedAddress] = useState<string>(userAddresses.data[0]?.id || "");
    const [addressList, setAddressList] = useState<UserAddress[]>(userAddresses.data);
    const [isNavigating, setIsNavigating] = useState(false);
    const [deliveryId, setDeliveryId] = useState(deliveryMethods[0]?.id || "")

    /**
     * Refresh address list from server after a new address is created
     */
    const handleAddressListUpdate = async (id?: string) => {
        try {
            console.log('Refreshing address list...');
            const updatedAddresses = await getUserAddressesAction();
            if (updatedAddresses.success && updatedAddresses.data) {
                setAddressList(updatedAddresses.data);
                if (id) {
                    setSelectedAddress(id);
                }
                console.log('Address list updated successfully:', updatedAddresses.data.length, 'addresses');
            } else {
                console.error('Failed to fetch updated addresses:', updatedAddresses);
            }
        } catch (error) {
            console.error('Failed to refresh address list:', error);
        }
    };

    return (
        <div className='max-md:px-3 flex md:mt-4 max-md:mt-5 md:justify-between max-md:flex-wrap max-md:gap-5'>

            <div className='md:w-[70%] max-md:w-full'>
                <UserAddresses
                    selectedAddress={selectedAddress}
                    setSelectedAddress={setSelectedAddress}
                    addressList={addressList}
                    onAddressListUpdate={handleAddressListUpdate}
                />
                <DeliveryMethodsList 
                    deliveryMethods={deliveryMethods}
                    deliveryId={deliveryId}
                    setDeliveryId={setDeliveryId}
                    />
            </div>
            <FactorCard
                selectedAddress={selectedAddress}
                steps={{title: "shipping", nextStepBtnTitle: "تایید آدرس و تکمیل سفارش", nextStepBtnLink: `/checkout/payment?address=${selectedAddress}&deliveryId=${deliveryId}`}}
                isNavigating={isNavigating}
                setIsNavigating={setIsNavigating}
                deliveryId={deliveryId}
                disabled={!selectedAddress || !deliveryId || addressList.length == 0}
            />
        </div>
    )
}

export default ShippingPageClient