html {
    scroll-behavior: smooth;
}

/* Removed unused .services-items and .text-bg classes */

/* Removed unused .title-with-pluses and .increase-amount classes */

/* Removed unused .footer class */

.broken-div {
    clip-path: polygon(0 100%, 100% 100%, 100% 0, 50% 20%, 0 0);

}

/* Removed unused .dashboard-sidbar and .active classes */

/* Removed unused .title-border, .card-details, and .search-products-filter classes */

/* Removed unused .title-bt-border class */

/* Removed unused .custom-checkbox classes */


/* Removed unused .product-options and .product-details classes */

.swiper {
    width: 100%;
    height: 100%;
}

.swiper-slide {
    height: auto !important;
}

.active-tab {
    background-color: #1F84FB;
    color: #fff;
    padding: 7px 10px;
    border-radius: 12px;
}

/* Removed unused .pluses class */

/* Removed unused .payment-card-body class */

.swiper {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.swiper-initialized {
    opacity: 1;
}

/* Removed unused .cart-circles class */

/* Removed unused .half-circle and .half-circle-payment-card classes */

table {
    width: 100%;
    border: 1px solid #d1d5db;
    /* tailwind's gray-300 */
    background-color: #ffffff;
    font-size: 0.875rem;
    /* text-sm */
    border-collapse: collapse;
}

table th,
table td {
    border: 1px solid #d1d5db;
    padding: 0.5rem;
    text-align: center;
}

table th {
    /* background-color: #e5e7eb; */
    /* tailwind's gray-200 */
}

table tbody tr:nth-child(odd) {
    background-color: #f3f4f6;
    /* tailwind's gray-100 */
}

@media (min-width: 768px) {
    .product-w-32 {
        width: 32%;
    }

    .about-service h2 {
        font-weight: 900;
        font-size: 1.5rem;
        line-height: 2rem;
    }

    .about-service p {
        line-height: 2rem;
    }

    .about-service div h2 {
        line-height: 2rem;
    }

}


/* Removed unused .checkbox-wrapper-15 classes */

/* Removed unused .full-border-bottom, .learn-more-title, .hollow-circle, and .sidebar-title classes */


/* Removed unused @keyframes and media queries for removed classes */

@media not all and (width >= 768px) {
    .about-service p {
        font-size: 15px;
        line-height: 28px;
    }

    .about-service h3 {
        font-size: 1.125rem;
    }
}