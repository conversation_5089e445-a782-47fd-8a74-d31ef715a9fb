import CookieService from "@/lib/services/cookie-service";
import APIError from "@/lib/types/classes/api-error";
import envConfig from "@/lib/config-env";

class APIClient {
    private async request<T>(
        endpoint: string,
        method: "GET" | "POST" | "PUT" | "DELETE" = "GET",
        body: Record<string, any> | null = null,
        serverCookies?: string,
        headers: Record<string, string> = {},
        cacheEnable?: boolean,
        baseURL?: string // New optional baseURL parameter
    ): Promise<T> {
        const env = envConfig();
        const url = `${baseURL ?? env.BASE_URL}${endpoint}`;

        const authToken = await CookieService.getAuthorizationToken();
        const options: RequestInit = {
            method,
            next: {revalidate: cacheEnable ? 300 : 0},
            headers: {
                "Content-Type": "application/json",
                "accept": "application/json",
                "X-Application-Token": env.X_APPLICATION_TOKEN,
                ...headers,
                ...(serverCookies ? {Cookie: serverCookies} : {}),
                ...(authToken ? {Authorization: `Bearer ${authToken}`} : {}),
            },
            credentials: "include",
        };

        if (body && (method === "POST" || method === "PUT")) {
            options.body = JSON.stringify(body);
        }

        try {
            const response = await fetch(url, options);

            const data: T = await response.json();
            console.log("######################", response);
            console.log("######################", data);
            if (!response.ok) {
                const errorMessage = (data as any)?.data?.message || "مشکل در ارتباط با سرور.";
                const callbackUrl = (data as any)?.data?.callbackUrl;
                throw new APIError(response.status, errorMessage, callbackUrl);
            }
            return data;
        } catch (error) {
            if (error instanceof APIError) {
                throw error;
            }

            throw new APIError(500, "مشکل در ارتباط با سرور.");
        }
    }

    get<T>(
        endpoint: string,
        serverCookies?: string,
        headers: Record<string, string> = {},
        cacheEnable?: boolean,
        baseURL?: string
    ): Promise<T> {
        return this.request<T>(endpoint, "GET", null, serverCookies, headers, cacheEnable, baseURL);
    }

    post<T>(
        endpoint: string,
        body: Record<string, any>,
        serverCookies?: string,
        headers?: Record<string, string>,
        baseURL?: string
    ): Promise<T> {
        return this.request<T>(endpoint, "POST", body, serverCookies, headers || {}, undefined, baseURL);
    }

    put<T>(
        endpoint: string,
        body: Record<string, any>,
        serverCookies?: string,
        headers?: Record<string, string>,
        baseURL?: string
    ): Promise<T> {
        return this.request<T>(endpoint, "PUT", body, serverCookies, headers || {}, undefined, baseURL);
    }

    delete<T>(
        endpoint: string,
        serverCookies?: string,
        headers: Record<string, string> = {},
        baseURL?: string
    ): Promise<T> {
        return this.request<T>(endpoint, "DELETE", null, serverCookies, headers, undefined, baseURL);
    }
}

export default new APIClient();