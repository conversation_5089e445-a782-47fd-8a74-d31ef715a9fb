import React from 'react'
import FavoritesPageItem from './FavoritesPageItem'
import { FavoriteItem } from '@/lib/types/favorites.types'

interface FavoritesPageListProps {
    favorites: FavoriteItem[]
    setFavoriteProducts: React.Dispatch<React.SetStateAction<FavoriteItem[]>>

}

const FavoritesPageList = ({favorites, setFavoriteProducts}: FavoritesPageListProps) => {
    return (
        <div className='mt-5 flex justify-between md:flex-wrap gap-y-3 max-md:flex-col'>
            {favorites.map((item, index) => (
                <FavoritesPageItem product={item} key={index} setFavoriteProducts={setFavoriteProducts} />
            ))}
            {/* <FavoritesPageItem />
            <FavoritesPageItem />
            <FavoritesPageItem />
            <FavoritesPageItem /> */}
        </div>
    )
}

export default FavoritesPageList