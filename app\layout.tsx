import type { <PERSON>ada<PERSON> } from "next";
import "../styles/globals.css";
import { Toaster } from "react-hot-toast";
import { UserProvider } from "@/lib/providers/UserProvider";
import { ServicesProvider } from "@/lib/providers/ServicesProvider";
import NextTopLoader from "nextjs-toploader";
import localFont from "next/font/local";
import Script from "next/script";
import ReactQueryProvider from "@/lib/providers/ReactQueryProvider";
import UtdComponent from "@/components/common/UtdComponent";
import PWAInitializer, { PWAInstallButton } from "@/components/common/PWAInitializer";
import NetworkStatusIndicator from "@/components/common/NetworkStatusIndicator";

import { Suspense } from "react";

const myCustomFont = localFont({
  src: [
    { path: "./assets/fonts/iransans/IRANSansWeb_Light.woff2", weight: "300" },
    { path: "./assets/fonts/iransans/IRANSansWeb_Medium.woff2", weight: "500" },
    { path: "./assets/fonts/iransans/IRANSansWeb_Bold.woff2", weight: "700" },
  ],
  display: "swap",
  variable: "--font-iransans",
  fallback: ["Tahoma", "Arial", "sans-serif"],
});

export const metadata: Metadata = {
  title: "khodrox",
  description: "خدمات آنلاین خودرو با خودراکس",
  icons: "/favicon.png",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fa" className={myCustomFont.className}>
      <head>
        {/* <Script id="gtm-head" strategy="afterInteractive">
                    {`(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','GTM-TVKNBV6Q');`}
                </Script> */}
        <link
          rel="manifest"
          type="application/manifest+json"
          href="/manifest.json"
        />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="theme-color" content="#f1efef" />
        <link rel="apple-touch-icon" href="/icons/icon512_maskable.png" />

        <link
          rel="preload"
          href="/assets/fonts/iransans/IRANSansWeb_Light.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/assets/fonts/iransans/IRANSansWeb_Medium.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/assets/fonts/iransans/IRANSansWeb_Bold.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />

        <link rel="preload" as="image" href="/assets/images/bg-gray.webp" />
        <link rel="preload" as="image" href="/assets/images/double-comma.png" />
        <link
          rel="preload"
          as="image"
          href="/assets/images/half-circle-yellow.png"
        />
        <link rel="preload" as="image" href="/assets/images/pluses.png" />
      </head>
      <body dir="rtl">
        <Suspense fallback={null}>
          <UtdComponent />
        </Suspense>

        {/* PWA Components */}
        {/* <PWAInitializer /> */}
        {/* <NetworkStatusIndicator /> */}
        {/* <PWAInstallButton /> */}

        <ReactQueryProvider>
          <NextTopLoader />
          <UserProvider>
            <ServicesProvider>{children}</ServicesProvider>
          </UserProvider>
          <Toaster position="bottom-left" />
        </ReactQueryProvider>
        <Script
          id="gtm-script"
          strategy="afterInteractive"
          src="https://www.googletagmanager.com/gtm.js?id=GTM-TVKNBV6Q"
        />

        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-TVKNBV6Q"
            height="0"
            width="0"
            style={{ display: "none", visibility: "hidden" }}
          ></iframe>
        </noscript>
      </body>
    </html>
  );
}
