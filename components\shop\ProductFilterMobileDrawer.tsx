import {useEffect, useRef} from "react";

type Props = {
    isOpen: boolean;
    onClose?: () => void;
    children: React.ReactNode;
    showCloseBtn?: boolean;
    halfScreen?: boolean; // New prop
}

export default function ProductFilterMobileDrawer({
  isOpen,
  onClose,
  children,
  showCloseBtn = true,
  halfScreen = false,
}: Props) {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose?.();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "hidden"; // جلوگیری از اسکرول
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = ""; // ریست overflow
    };
  }, [isOpen]);

  if (!isOpen) return null; // ❗ جلوگیری از رندر زمانی که بسته است

  return (
    <>
      <div
        ref={menuRef}
        className={`lg:hidden fixed inset-x-0 ${
          halfScreen ? "top-1/3 h-2/3" : "top-0 h-full"
        } border-t bg-white shadow-lg z-[52] flex flex-col w-full`}
      >
        {/* Close Button */}
        {showCloseBtn && (
          <div className="flex w-[95%] mt-1 justify-end z-[100]">
            <button
              onClick={() => onClose?.()}
              className="text-gray-600 hover:text-black text-2xl font-bold"
              aria-label="Close filter drawer"
            >
              &times;
            </button>
          </div>
        )}

        {/* Scrollable content */}
        <div className="overflow-y-auto flex-1 px-3 pb-6">
          {children}
        </div>
      </div>

      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-[51] md:hidden"
        onClick={() => onClose?.()}
      />
    </>
  );
}
