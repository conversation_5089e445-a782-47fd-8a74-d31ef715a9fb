'use client';
import { useEffect, useRef } from 'react';
import clsx from 'clsx';
import  { StaticImageData } from 'next/image';
import { PencilLine } from 'lucide-react';

type AddressItemProps = {
  id: string;
  selected: string | null;
  onSelect: (id: string) => void;
  image: StaticImageData;
  selectable?: boolean;
  address?: string;
  province?: string;
  city?: string;
  receiver_name?: string;
  editable?: boolean;
  receiver_phone: string;
};

export default function UserAddressItem({ id, selected, onSelect, selectable, address, province, city, receiver_name, receiver_phone,editable }: AddressItemProps) {
  const checkboxRef = useRef<HTMLInputElement | null>(null);
  const isChecked = selected === id;

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Only prevent default if the click is on the checkbox or label
    const target = e.target as HTMLElement;
    const isCheckboxOrLabel = target.tagName === 'INPUT' ||
      target.tagName === 'LABEL' ||
      target.closest('label') !== null ||
      target.closest('.checkbox-wrapper-15') !== null;

    if (isCheckboxOrLabel) {
      return;
    }
    onSelect(id);
  };

  useEffect(() => {
    if (checkboxRef.current) {
      checkboxRef.current.checked = isChecked;
    }
  }, [isChecked]);


  return (
    <div
      onClick={handleClick}
      className={clsx(
        "mt-3 text-gray-400 p-3 max-md:py-2 rounded-3xl border cursor-pointer transition-all duration-300",
        selectable &&
        (isChecked
          ? "border-[#3c53c7] shadow-sm scale-[1.01] bg-white"
          : "border-gray-200 hover:border-[#3c53c7]")
      )}
      data-id={id}
      data-selectable={selectable ? 'true' : 'false'}
      data-selected={isChecked ? 'true' : 'false'}
    >
      <div className="payment-method-item flex justify-between items-center max-md:text-sm p-3">
        <div className="flex md:gap-5 max-md:gap-2 items-center">
          {/* <div className=" md:px-4 md:py-1 rounded-2xl">
            <Image className='' src={image} alt='' />
          </div> */}
          <div className="flex flex-col gap-3 max-md:gap-1">
            <span className='text-primary text-sm max-md:text-sm'>
              {receiver_name ? <p> گیرنده: {receiver_name} - {receiver_phone}</p> : 'آدرس تحویل سفارش'}
            </span>
            <div className="text-sm font-extralight">
              <p className='flex flex-col max-md:text-sm !leading-6'>
                  {address ?
                    `${province || ''} ${city ? `/ ${city}` : ''} ${address ? `/ ${address}` : ''}` :
                    ''}
                
                {
                  editable &&
                  <span className="text-sm max-md:text-xs mt-2 max-md:mt-1 flex items-center gap-2">
                    <PencilLine size={18} /> ویرایش آدرس
                  </span>
                }
              </p>
            </div>
          </div>
        </div>
        <div className={clsx({ hidden: !selectable })}>
          <div className="checkbox-wrapper-15">
            <input
              ref={checkboxRef}
              className="inp-cbx"
              id={`cbx-${id}`}
              type="checkbox"
              style={{ display: "none" }}
            />
            <label className="cbx" htmlFor={`cbx-${id}`}>
              <span>
                <svg width="12px" height="9px" viewBox="0 0 12 9">
                  <polyline points="1 5 4 8 11 1"></polyline>
                </svg>
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}
