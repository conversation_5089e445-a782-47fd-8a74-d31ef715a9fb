'use client';

import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import { useEffect, useRef, useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import BlogSliderCard from './BlogSliderCard';
import RoundedArrow from '../common/svg/RoundedArrow';
import { Article } from '@/lib/types/types';


const products = Array(7).fill({});


export default function BlogSliderSection({articles}: {articles: Article[]}) {
    const prevRef = useRef<HTMLButtonElement>(null);
    const nextRef = useRef<HTMLButtonElement>(null);
    const [swiperInitialized, setSwiperInitialized] = useState(false);
    const [isBeginning, setIsBeginning] = useState(true);
    const [isEnd, setIsEnd] = useState(false);
    const [activeIndex, setActiveIndex] = useState(0);
    const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('mobile');

    useEffect(() => {
        const handleResize = () => {
            const width = window.innerWidth;

            // Better device detection for Surface Pro and similar devices
            if (width < 640) {
                setDeviceType('mobile');
            } else if (width >= 640 && width <= 1024) {
                setDeviceType('tablet');
            } else {
                setDeviceType('desktop');
            }
        };

        handleResize();
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    return (
        <section className="w-full py-6">
            <div className="flex px-8 mb-4">
                <h2 className="md:text-xl font-semibold ">مطالب پیشنهادی سر دبیر</h2>
                <RoundedArrow />
            </div>

            <div dir="rtl" className="relative px-4 md:min-h-[430px] overflow-hidden" >
                {/* Navigation Buttons */}
                <div className="flex gap-2 justify-end mb-5">
                    <button
                        ref={prevRef}
                        className={`w-8 h-8 rounded-full bg-white border border-gray-200 shadow transition-all flex items-center justify-center ${isBeginning ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'
                            }`}
                        disabled={isBeginning}
                    >
                        <ChevronRight className="w-4 h-4 text-gray-500" />
                    </button>
                    <button
                        ref={nextRef}
                        className={`w-8 h-8 rounded-full bg-white border border-gray-200 shadow transition-all flex items-center justify-center ${isEnd ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'
                            }`}
                        disabled={isEnd}
                    >
                        <ChevronLeft className="w-4 h-4 text-gray-500" />
                    </button>
                </div>

                {!swiperInitialized && (
                    <div className="flex gap-4 animate-pulse">
                        {products.map((_, i) => (
                            <div key={i} className="w-[280px] h-[300px] bg-gray-100 rounded-lg" />
                        ))}
                    </div>
                )}

                <Swiper
                    modules={[Navigation]}
                    navigation={{
                        prevEl: prevRef.current,
                        nextEl: nextRef.current,
                    }}
                    onBeforeInit={(swiper) => {
                        swiper.params.navigation = swiper.params.navigation || {};
                        Object.assign(swiper.params.navigation, {
                            prevEl: prevRef.current,
                            nextEl: nextRef.current,
                        });
                    }}
                    onInit={(swiper) => {
                        setSwiperInitialized(true);
                        swiper.el.classList.add('swiper-initialized');
                        swiper.update();
                        setIsBeginning(swiper.isBeginning);
                        setIsEnd(swiper.isEnd);
                    }}
                    onSlideChange={(swiper) => {
                        setActiveIndex(swiper.activeIndex);
                        setIsBeginning(swiper.isBeginning);
                        setIsEnd(swiper.isEnd);
                    }}
                    spaceBetween={16}
                    slidesPerView={1.2}
                    observer={true}
                    observeParents={true}
                    breakpoints={{
                        640: {
                            slidesPerView: 2.2,
                            spaceBetween: 20
                        },
                        768: {
                            slidesPerView: 2.5,
                            spaceBetween: 24
                        },
                        1024: {
                            slidesPerView: 3.2,
                            spaceBetween: 24
                        },
                        1280: {
                            slidesPerView: 4,
                            spaceBetween: 24
                        },
                        // Surface Pro 7 and similar tablet devices
                        1366: {
                            slidesPerView: 3.5,
                            spaceBetween: 28
                        },
                        1440: {
                            slidesPerView: 4.2,
                            spaceBetween: 28
                        }
                    }}
                    className={`blog-slider-container swiper-container mt-10 transition-opacity duration-300 ${!swiperInitialized ? 'invisible opacity-0' : 'opacity-100'}`}
                >
                    {articles?.length && 
                    articles.map((item, index) => (
                        <SwiperSlide key={index} className='h-[22rem] mt-5' style={{ overflow: 'visible' }}>

                            <BlogSliderCard
                                {...item}
                                isActive={index === (
                                    deviceType === 'mobile'
                                        ? activeIndex
                                        : deviceType === 'tablet'
                                            ? activeIndex + 1
                                            : activeIndex + 1
                                )}
                            />
                            {/* <BlogSliderCard isActive={index === (isMobile ? activeIndex : activeIndex + 1)} /> */}
                        </SwiperSlide>
                    ))}
                </Swiper>
            </div>
        </section>
    );
}