import { useCart } from '@/lib/context/cart-context';
import { DeliveryMethod } from '@/lib/types/types';
import { Truck } from 'lucide-react';
import Image from 'next/image';
import React, { useEffect } from 'react';

interface DeliveryMethodsListProps {
  deliveryMethods: DeliveryMethod[];
  deliveryId: string;
  setDeliveryId: (id: string) => void;
}

const DeliveryMethodsList = ({
  deliveryMethods,
  deliveryId,
  setDeliveryId,
}: DeliveryMethodsListProps) => {
  const { setDeliveryPrice } = useCart()

  useEffect(() => {
      setDeliveryPrice(deliveryMethods.find(item => item.id === deliveryId)?.price || 0)  
  }, [])
  

  const handleDeliveryChange = (id: string) => {
    setDeliveryId(id);
    setDeliveryPrice(deliveryMethods.find(item => item.id === id)?.price || 0) // finding the price of the selected delivery method
    
  };

  return (
    <div className="max-md:w-full bg-white rounded-3xl mt-5 md:p-8 max-md:p-5 md:min-h-[28rem] max-md:min-h-80">
      <div className="header flex justify-between">
        <div className="flex flex-col gap-3 relative pb-5 max-md:pb-3 title-bt-border">
          <h2 className="text-xl font-black max-md:text-base">نحوه ارسال</h2>
        </div>
        <Truck className="max-md:w-10 max-md:h-10" size={60} />
      </div>

      <div className="mt-8 md:flex-wrap flex md:gap-5 max-md:gap-3 max-md:overflow-x-auto max-md:py-3 max-md:px-1">
        {deliveryMethods?.map((item) => {
          const isSelected = deliveryId === item.id;

          return (
            <label
              key={item.id}
              onClick={() => handleDeliveryChange(item.id)}
              className={`
                relative border w-44 md:w-[25%] max-md:shrink-0 md:h-44 flex flex-col justify-center items-center cursor-pointer 
                p-5 rounded-3xl transition-all duration-300
                ${isSelected ? 'border-primary bg-primary/10 scale-[1.03]' : 'border-gray-300 hover:border-primary'}
              `}
            >
              
              <div className="absolute top-4 left-4 w-5 h-5 rounded-full border border-gray-400 flex items-center justify-center bg-white">
                {isSelected && (
                  <svg
                    width="12px"
                    height="9px"
                    viewBox="0 0 12 9"
                    fill="none"
                    stroke="#2563EB"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <polyline points="1 5 4 8 11 1" />
                  </svg>
                )}
              </div>

              <div className="flex flex-col gap-3 items-center max-md:mt-3">
                <div className="relative w-full h-[50px]">
                  <Image
                    className="object-contain h-full"
                    fill
                    src={item.image_url || ''}
                    alt={item.title || 'delivery method'}
                  />
                </div>
                <div className="flex flex-col gap-2 text-sm text-center">
                  <p className={`md:text-base ${isSelected ? 'text-primary font-bold' : ''}`}>
                    {item.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {item.price.toLocaleString()} تومان
                  </p>
                </div>
              </div>
            </label>
          );
        })}
      </div>
    </div>
  );
};

export default DeliveryMethodsList;
