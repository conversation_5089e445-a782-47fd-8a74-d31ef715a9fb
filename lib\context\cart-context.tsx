"use client"
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { SelectedVariant, Variation } from '@/lib/types/product.types';
import { useAuth } from '@/lib/hooks/useAuth';
import { getUserCart, addToCard, decreaseFromCartAction, addMultipleToCart } from '@/actions/cart.action';
import { DiscountCode } from '@/actions/payment.action';


export interface CartApiItem {
  variant?: Variation;
  quantity: number;
  attributes?: SelectedVariant[];
  id?: string;
  image?: string;
  name?: string
  sale_price: number | null
  price: number
  product_id?: string;
  created_at?: string;
  updated_at?: string;
  shop_name?: string;
  guarantee?: {
    months: string
    company_name: string
    price: string
  }
  [key: string]: unknown; // Allow for any additional properties with safer type
}

export interface CartApiResponse {
  success: boolean;
  data?: {
    items?: CartApiItem[];
    total?: number
    total_discount?: number
    user_id?: string
    subtotal?: number
  };
  message: string
  status: number
  error?: string;
}

// Use the same type for our local cart items
type CartItem = CartApiItem;

interface CartContextType {
  cartItems: CartItem[];
  totalItems: number;
  totalPrice: number;
  finalPrice: number
  totalDiscount: number
  isLoading: boolean;
  isInitialized: boolean;
  isUserContextInitialized: boolean;
  isUpdating: "add" | "decrease" | null; // New loading state for cart operations
  addToCart: (variant: Variation, quantity?: number, productInfo?: { image?: string; name?: string }) => void;
  checkoutAddToCart: (product: { id: string, quantity: number }) => void;
  checkoutDecreaseFromCart: (product_id: string) => void;
  removeFromCart: (variantId: string) => void;
  decreaseFromCart: (variant: Variation) => void;
  updateQuantity: (variantId: string, newQuantity: number) => void;
  clearCart: () => void;
  reloadCart: (discount?: string | null | undefined) => Promise<CartApiResponse | undefined>;
  deliveryPrice: number;
  setDeliveryPrice: (price: number) => void;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider = ({ children }: { children: ReactNode }) => {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [cartResponse, setCartResponse] = useState<CartApiResponse>();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [isUpdating, setIsUpdating] = useState<"add" | "decrease" | null>(null);
  const { isLoggedIn, userData } = useAuth();
  const [deliveryPrice, setDeliveryPrice] = useState<number>(0);

  const isUserContextInitialized = userData !== undefined;

  useEffect(() => {
    if (!isUserContextInitialized) return;

    const loadCart = async () => {
      setIsLoading(true);

      try {
        if (isLoggedIn) {
          const savedCart = localStorage.getItem('cart') || ""
          const parsedSavedCart: CartItem[] = savedCart ? JSON.parse(savedCart) : []
          setCartItems(parsedSavedCart)
          const cartData = await getUserCart() as CartApiResponse;
          console.log(cartData);


          if (cartData.data?.items?.length) {
            setCartResponse(cartData)
            if (cartData && cartData.success !== false) {
              const apiCartItems = cartData.data?.items || [];
              setCartItems(apiCartItems);
            }
          } else {
            if (parsedSavedCart.length) {

              const payload = parsedSavedCart
                // .filter(item => typeof item.id === 'string')
                .map(item => ({
                  variant_id: item.id as string,
                  quantity: item.quantity,
                }));
              const multipleAddResponse = await addMultipleToCart(payload);


              if (multipleAddResponse.success) {
                localStorage.removeItem('cart');
                const updatedCartData = await getUserCart() as CartApiResponse;
                if (updatedCartData && updatedCartData.success !== false) {
                  setCartResponse(updatedCartData);
                  setCartItems(updatedCartData.data?.items || []);
                }
              }
            }
          }
        } else {
          const savedCart = localStorage.getItem('cart');
          if (savedCart) {
            setCartItems(JSON.parse(savedCart));
          }
        }
      } catch (error) {
        console.error('Error loading cart data:', error);
      } finally {
        setIsLoading(false);
        setIsInitialized(true);
      }
    };

    loadCart();
  }, [isLoggedIn, isUserContextInitialized]);

  // Only save to localStorage when cartItems change AND we're not in the initial loading phase
  useEffect(() => {
    if (!isInitialized || isLoggedIn) return;

    localStorage.setItem('cart', JSON.stringify(cartItems));
  }, [cartItems, isLoggedIn, isInitialized]);


  const reloadCart = async (discount: string | null | undefined) => {
    if (!isUserContextInitialized) return;

    setIsLoading(true);

    try {
      if (isLoggedIn) {

        const cartData = discount ? await DiscountCode(discount) : await getUserCart() as CartApiResponse;
        if (cartData && cartData.success !== false) {
          setCartResponse(cartData);
          setCartItems(cartData.data?.items || []);
        }
        return cartData
      } else {
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
          setCartItems(JSON.parse(savedCart));
        }
      }

    } catch (error) {
      console.error('Error reloading cart:', error);
    } finally {
      setIsLoading(false);
    }
  };


  const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);

  // Calculate prices based on cart items when not logged in, otherwise use API response
  const calculatePrices = () => {
    if (!isLoggedIn) {
      const subtotal = cartItems.reduce((sum, item) => {
        return sum + (item.price * item.quantity);
      }, 0);

      const totalDiscount = cartItems.reduce((sum, item) => {
        if (item.sale_price) {
          return sum + ((item.price - item.sale_price) * item.quantity);
        }
        return sum;
      }, 0);

      return {
        subtotal,
        totalDiscount,
        total: subtotal - totalDiscount + deliveryPrice
      };
    }

    return {
      subtotal: cartResponse?.data?.subtotal || 0,
      totalDiscount: cartResponse?.data?.total_discount || 0,
      total: cartResponse?.data?.total ? cartResponse?.data?.total + deliveryPrice : 0
    };
  };

  const { subtotal: totalPrice, totalDiscount, total: finalPrice } = calculatePrices();

  const checkoutAddToCart = async (product: { id: string, quantity: number }) => {
    setIsUpdating("add");

    try {
      // Update UI immediately for better UX
      setCartItems(prevItems => {
        const existingItem = prevItems.find(item => item.id === product.id);

        if (existingItem) {
          return prevItems.map(item =>
            item.id === product.id
              ? { ...item, quantity: item.quantity + product.quantity }
              : item
          );
        }
        return prevItems;
      });

      if (isLoggedIn) {
        const variationForApi: Partial<Variation> = {
          id: product.id
        };

        const response = await addToCard(variationForApi as Variation);
        if (!response.success) {
          // If API call fails, revert the UI change
          setCartItems(prevItems => {
            const existingItem = prevItems.find(item => item.id === product.id);
            if (existingItem && existingItem.quantity >= product.quantity) {
              return prevItems.map(item =>
                item.id === product.id
                  ? { ...item, quantity: item.quantity - product.quantity }
                  : item
              );
            }
            return prevItems;
          });
          console.error('Failed to add item to cart via API');
        } else {
          // Refresh cart data from server to update finalPrice
          const updatedCartData = await getUserCart() as CartApiResponse;
          if (updatedCartData && updatedCartData.success !== false) {
            setCartResponse(updatedCartData);
            setCartItems(updatedCartData.data?.items || []);
          }
        }
      }
    } catch (error) {
      console.error('Error adding item to cart:', error);
      // Revert UI changes on error
      setCartItems(prevItems => {
        const existingItem = prevItems.find(item => item.id === product.id);
        if (existingItem && existingItem.quantity >= product.quantity) {
          return prevItems.map(item =>
            item.id === product.id
              ? { ...item, quantity: item.quantity - product.quantity }
              : item
          );
        }
        return prevItems;
      });
    } finally {
      setIsUpdating(null);
    }
  }
  const addToCart = async (variant: Variation, quantity: number = 1, productInfo?: { image?: string; name?: string }) => {
    setIsUpdating("add");

    try {
      // Update UI immediately for better UX
      setCartItems(prevItems => {
        const existingItem = prevItems.find(item => item.id === variant.id);

        if (existingItem) {
          return prevItems.map(item =>
            item.id === variant.id
              ? { ...item, quantity: item.quantity + quantity }
              : item
          );
        }

        // Add a new CartApiItem with the required 'variant' property and product info
        return [
          ...prevItems,
          {
            color: variant.color,
            current_quantity: variant.current_quantity,
            id: variant.id,
            price: variant.price,
            sale_price: variant.sale_price,
            size: variant.size,
            sku: variant.sku,
            quantity,
            image: productInfo?.image,
            name: productInfo?.name,
            attributes: variant.attributes
          }
        ];

      });

      if (isLoggedIn) {
        const response = await addToCard(variant);
        if (!response.success) {
          // If API call fails, revert the UI change
          setCartItems(prevItems => {
            const existingItem = prevItems.find(item => item.id === variant.id);
            if (existingItem && existingItem.quantity >= quantity) {
              if (existingItem.quantity === quantity) {
                return prevItems.filter(item => item.id !== variant.id);
              } else {
                return prevItems.map(item =>
                  item.id === variant.id
                    ? { ...item, quantity: item.quantity - quantity }
                    : item
                );
              }
            }
            return prevItems;
          });
          console.error('Failed to add item to cart via API');
        } else {
          // Refresh cart data from server to update finalPrice
          const updatedCartData = await getUserCart() as CartApiResponse;
          if (updatedCartData && updatedCartData.success !== false) {
            setCartResponse(updatedCartData);
            setCartItems(updatedCartData.data?.items || []);
          }
        }
      }

    } catch (error) {
      console.error('Error adding item to cart:', error);
      setCartItems(prevItems => {
        const existingItem = prevItems.find(item => item.id === variant.id);
        if (existingItem && existingItem.quantity >= quantity) {
          if (existingItem.quantity === quantity) {
            return prevItems.filter(item => item.id !== variant.id);
          } else {
            return prevItems.map(item =>
              item.id === variant.id
                ? { ...item, quantity: item.quantity - quantity }
                : item
            );
          }
        }
        return prevItems;
      });
    } finally {
      setIsUpdating(null);
    }
  };


  const removeFromCart = async (variantId: string) => {
    setCartItems(prevItems =>
      prevItems.filter(item => item.id !== variantId)
    );


    if (isLoggedIn) {
      try {
        //  await removeFromCard(variantId);
      } catch (error) {
        console.error('Error removing item from cart via API:', error);
      }
    }
  };


  const updateQuantity = async (variantId: string, newQuantity: number) => {
    // if (newQuantity <= 0) {
    //   removeFromCart(variantId);
    //   return;
    // }


    setCartItems(prevItems =>
      prevItems.map(item =>
        item.id === variantId
          ? { ...item, quantity: newQuantity }
          : item
      )
    );


    if (isLoggedIn) {
      try {

      } catch (error) {
        console.error('Error updating item quantity via API:', error);
      }
    }
  };


  const checkoutDecreaseFromCart = async (product_id: string) => {
    if (!product_id) return;

    setIsUpdating("decrease");

    try {
      const cartItem = cartItems.find(item => item.id === product_id);
      if (!cartItem) return;

      const newQuantity = cartItem.quantity - 1;

      // Update UI immediately for better UX
      if (newQuantity <= 0) {
        setCartItems(prevItems => prevItems.filter(item => item.id !== product_id));
      } else {
        // Decrease quantity
        setCartItems(prevItems =>
          prevItems.map(item =>
            item.id === product_id
              ? { ...item, quantity: newQuantity }
              : item
          )
        );
      }

      if (isLoggedIn) {
        const response = await decreaseFromCartAction(product_id);
        if (!response.success) {
          setCartItems(prevItems => {
            const existingItem = prevItems.find(item => item.id === product_id);
            if (existingItem) {
              // Item exists, just increase quantity back
              return prevItems.map(item =>
                item.id === product_id
                  ? { ...item, quantity: item.quantity + 1 }
                  : item
              );
            } else {
              // Item was removed, add it back
              return [
                ...prevItems,
                {
                  ...cartItem,
                  quantity: cartItem.quantity
                }
              ];
            }
          });
          console.error('Failed to decrease item from cart via API');
        } else {
          // Refresh cart data from server to update finalPrice
          const updatedCartData = await getUserCart() as CartApiResponse;
          if (updatedCartData && updatedCartData.success !== false) {
            setCartResponse(updatedCartData);
            setCartItems(updatedCartData.data?.items || []);
          }
        }
      }
    } catch (error) {
      console.error('Error decreasing item from cart:', error);
      const cartItem = cartItems.find(item => item.id === product_id);
      if (cartItem) {
        setCartItems(prevItems => {
          const existingItem = prevItems.find(item => item.id === product_id);
          if (existingItem) {
            return prevItems.map(item =>
              item.id === product_id
                ? { ...item, quantity: item.quantity + 1 }
                : item
            );
          } else {
            return [
              ...prevItems,
              {
                ...cartItem,
                quantity: cartItem.quantity
              }
            ];
          }
        });
      }
    } finally {
      setIsUpdating(null);
    }
  }
  const decreaseFromCart = async (variant: Variation) => {
    if (!variant.id) return;

    setIsUpdating("decrease");

    try {
      const cartItem = cartItems.find(item => item.id == variant.id);
      if (!cartItem) return;

      const newQuantity = cartItem.quantity - 1;

      // Update UI immediately for better UX
      if (newQuantity <= 0) {
        setCartItems(prevItems => prevItems.filter(item => item.id != variant.id));
      } else {
        setCartItems(prevItems =>
          prevItems.map(item =>
            item.id === variant.id
              ? { ...item, quantity: newQuantity }
              : item
          )
        );
      }

      if (isLoggedIn) {
        const response = await decreaseFromCartAction(variant.id);
        if (!response.success) {
          // If API call fails, revert the UI change
          setCartItems(prevItems => {
            const existingItem = prevItems.find(item => item.id == variant.id);
            if (existingItem) {
              // Item exists, just increase quantity back
              return prevItems.map(item =>
                item.id === variant.id
                  ? { ...item, quantity: item.quantity + 1 }
                  : item
              );
            } else {
              // Item was removed, add it back
              return [
                ...prevItems,
                {
                  ...cartItem,
                  quantity: cartItem.quantity
                }
              ];
            }
          });
          console.error('Failed to decrease item from cart via API');
        } else {
          // Refresh cart data from server to update finalPrice
          const updatedCartData = await getUserCart() as CartApiResponse;
          if (updatedCartData && updatedCartData.success !== false) {
            setCartResponse(updatedCartData);
            setCartItems(updatedCartData.data?.items || []);
          }
        }
      }
    } catch (error) {
      console.error('Error decreasing item from cart:', error);
      // Revert UI changes on error
      const cartItem = cartItems.find(item => item.id === variant.id);
      if (cartItem) {
        setCartItems(prevItems => {
          const existingItem = prevItems.find(item => item.id === variant.id);
          if (existingItem) {
            return prevItems.map(item =>
              item.id === variant.id
                ? { ...item, quantity: item.quantity + 1 }
                : item
            );
          } else {
            return [
              ...prevItems,
              {
                ...cartItem,
                quantity: cartItem.quantity
              }
            ];
          }
        });
      }
    } finally {
      setIsUpdating(null);
    }
  };

  const clearCart = async () => {
    setCartItems([]);

    if (isLoggedIn) {
      try {
      } catch (error) {
        console.error('Error clearing cart via API:', error);
      }
    }
  };

  return (
    <CartContext.Provider
      value={{
        cartItems,
        deliveryPrice,
        setDeliveryPrice,
        totalItems,
        totalPrice,
        finalPrice,
        totalDiscount,
        isLoading,
        isInitialized,
        isUserContextInitialized,
        isUpdating,
        addToCart,
        checkoutAddToCart,
        checkoutDecreaseFromCart,
        removeFromCart,
        decreaseFromCart,
        updateQuantity,
        clearCart,
        reloadCart
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};