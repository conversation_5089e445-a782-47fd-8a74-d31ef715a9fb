import BlogComments from '@/components/blog/SinglePage/BlogComments'
import ChildSchema from '@/components/common/ChildSchema'
import DrivingLicenseComponent from '@/components/DrivingLicensePoint/DrivingLicenseComponent'
import ArticleSection from '@/components/InquiryStaticComponents/ArticleSection'
import Faq from '@/components/InquiryStaticComponents/Faq'
// import envConfig from '@/lib/config-env'
import { PageContentResponse } from '@/lib/types/types'
import { getPageContent } from '@/lib/utils'


export async function generateMetadata() {
    const data = await getPageContent("motor-driving-license-point");

    return {
        title: data.meta_title,
        description: data.meta_description,
         keywords: data.tags || [],
    ...(data.meta_search && { robots: data.meta_search }),
    ...(data.canonical && { alternates: { canonical: data.canonical } }),
    };
}

const MotorDrivingLicensePointPage = async () => {
//   const env = envConfig()
    const status = process.env.NEXT_PUBLIC_MOTOR_DRIVING_LICENSE_POINT!
    const data: PageContentResponse = await getPageContent("motor-driving-license-point")
    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id="motor-driving-license-point"
                    schema={schema}
                />
            }

            <DrivingLicenseComponent title={title || ""} status={status} />
            {/* <DrivingLicensePoint /> */}
            {
                description &&
                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
            <div className="md:max-w-7xl mx-auto mb-10 px-3">
                  {/* <UserComments /> */}
                  <BlogComments contentId={data.id} />
            </div>
        </>
    );
}

export default MotorDrivingLicensePointPage