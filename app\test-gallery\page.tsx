'use client';

import ProductGallery from '@/components/shop/ProductPage/ProductGallery';
import { Gallery } from '@/lib/types/product.types';

// Sample gallery data for testing
const sampleImages: Gallery[] = [
  {
    url: 'https://via.placeholder.com/600x400/FF6B6B/FFFFFF?text=Image+1',
    caption: 'Sample Image 1'
  },
  {
    url: 'https://via.placeholder.com/600x400/4ECDC4/FFFFFF?text=Image+2',
    caption: 'Sample Image 2'
  },
  {
    url: 'https://via.placeholder.com/600x400/45B7D1/FFFFFF?text=Image+3',
    caption: 'Sample Image 3'
  },
  {
    url: 'https://via.placeholder.com/600x400/96CEB4/FFFFFF?text=Image+4',
    caption: 'Sample Image 4'
  },
  {
    url: 'https://via.placeholder.com/600x400/FFEAA7/000000?text=Image+5',
    caption: 'Sample Image 5'
  }
];

/**
 * Test page for ProductGallery component with dynamic Swiper loading
 * This page is used to verify that the dynamic loading is working correctly
 */
export default function TestGalleryPage() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold text-center mb-8">
        ProductGallery Dynamic Loading Test
      </h1>
      
      <div className="max-w-2xl mx-auto">
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Test Information:</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>This page tests the dynamic loading of Swiper components</li>
            <li>The gallery should show a loading spinner initially</li>
            <li>Then load the Swiper gallery with sample images</li>
            <li>Check browser dev tools Network tab to see dynamic chunk loading</li>
            <li>Check console for any errors</li>
          </ul>
        </div>

        <ProductGallery images={sampleImages} />
        
        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Expected Behavior:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>✅ Loading spinner appears first</li>
            <li>✅ Gallery loads with 3 main images visible</li>
            <li>✅ Thumbnails show below (desktop only)</li>
            <li>✅ "+2" indicator for additional images</li>
            <li>✅ Click on images opens modal</li>
            <li>✅ Modal has navigation and thumbnails</li>
            <li>✅ Swiper chunks loaded dynamically (check Network tab)</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
