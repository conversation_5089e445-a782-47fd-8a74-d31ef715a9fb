@tailwind base;
@tailwind components;
@tailwind utilities;

/* Essential global styles only */

.broken-div {
    clip-path: polygon(0 100%, 100% 100%, 100% 0, 50% 20%, 0 0);
}

* {
    direction: rtl;
}

html {
  scroll-behavior: smooth;
  overscroll-behavior-y: none;
}

.borderless-input {
    @apply !border-none focus-visible:!ring-offset-0 focus-visible:!ring-0 focus-visible:!outline-none;
}

.left-direction {
    direction: ltr !important;
}

.login-form {
    background-image: url("/assets/images/fade-logo.png");
    background-repeat: no-repeat;
    background-position-x: 20px;
    background-position-y: 90px;
}

/* Article section styles - used in ArticleSection component */
.article-section h1 {
    font-size: 32px !important;
    font-weight: 700 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h2 {
    font-size: 28px !important;
    font-weight: 600 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h3 {
    font-size: 24px !important;
    font-weight: 600 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h4 {
    font-size: 20px !important;
    font-weight: 500 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h5 {
    font-size: 16px !important;
    font-weight: 400 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

.article-section h6 {
    font-size: 14px !important;
    font-weight: 400 !important;
    margin-top: 2rem !important; 
    margin-bottom: 1rem !important;
}

/* Figure styles - used in content */
figure {
    margin: 0 auto;
}
figure img {
    border-radius: 5%;
    width: 100% !important;
}

/* Blockquote styles */
blockquote {
  background-color: #F5F6F8;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
  border-radius: 1.5rem;
  background-image: url('/assets/images/double-comma.png');
  background-repeat: no-repeat;
  background-size: 70px auto;
  background-position: 90px 40px;
}

@media (max-width: 768px) {
  blockquote {
    width: 96%;
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    background-image: none;
    background-position: 40px 90px;
  }
}

blockquote p {
  text-align: justify;
  line-height: 2rem;
  color:#302e2e !important;
}

@media (max-width: 768px) {
  blockquote p {
    line-height: 1.75rem;
    color:#302e2e !important;
  }
}

/* SEO Section styles - used in ArticleSection */
.SeoSection h2{
    padding-right: 1.5rem;
    border-right: 0.3rem solid #1F84FB;
}
.SeoSection a {
    color: #1F84FB;
}

.SeoSection figure {
  overflow-y: auto;
  max-width: 100%;
}

.SeoSection ul {
    list-style: none;
    padding-right: 1.5rem;
}
  
.SeoSection ul li {
    position: relative;
    padding-right: 1rem;
    line-height: 1.6;
    margin-right: 20px;
    margin-bottom: 10px;
}
  
.SeoSection ul li::before {
    content: '•';
    position: absolute;
    right: -15px;
    top: 65%;
    transform: translateY(-50%);
    color: #1F84FB;
    font-size: 2.5em;
    line-height: 1;
}
  
.SeoSection ul li[data-list="checked"]::before {
    content: '';
    width: 0.6em;
    height: 0.6em;
    border: 2px solid #1F84FB;
    border-radius: 50%;
    background: transparent;
    font-size: initial;
}
  
.SeoSection ol li {
    list-style: decimal;
    margin-bottom: 1rem;
}
.SeoSection ol {
    padding-right: 1.5rem;
}
.SeoSection ol li::marker {
    color: #3c53c7;
    font-weight: bold;
    font-size: 1.2rem;
}

.blog-pluses::before {
    content: "";
    position: absolute;
    top: 5px;
    right: -1px;
    width: 65px;
    height: 95%;
    background-image: url("/assets/images/pluses.png");
    background-repeat: repeat-y;
    background-position: right;
}

.ltr {
  direction: ltr;
}

.bg-gray-hero {
  background-color: #f5f5f5; 
  background-image: url('/assets/images/bg-gray.webp');
  background-position: bottom;
  background-size: cover;
  background-repeat: no-repeat;
}

/* CSS Variables */
@layer base {
    :root {
        --background: #F5F6F8;
        --foreground: #62676E;
        --card: 0 0% 100%;
        --card-foreground: 240 10% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 240 10% 3.9%;
        --primary: #1F84FB;
        --primary-foreground: #0A0A0C;
        --secondary: 240 4.8% 95.9%;
        --secondary-foreground: 240 5.9% 10%;
        --muted: #F9FAFB;
        --muted-foreground: #9DA5B0;
        --accent: 240 4.8% 95.9%;
        --accent-foreground: #15192A;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: #E4E6E9;
        --input: 240 5.9% 90%;
        --ring: 240 10% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
    }

    .dark {
        --background: 240 10% 3.9%;
        --foreground: 0 0% 98%;
        --card: 240 10% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 240 10% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 240 5.9% 10%;
        --secondary: 240 3.7% 15.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 240 3.7% 15.9%;
        --muted-foreground: 240 5% 64.9%;
        --accent: 240 3.7% 15.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 240 3.7% 15.9%;
        --input: 240 3.7% 15.9%;
        --ring: 240 4.9% 83.9%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
    }
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/* Mobile responsive adjustments */
@media not all and (width >=768px) {
    .blog-pluses::before {
        display: none;
    }
    .article-section h2 {
        font-size: 1.2rem !important;
    }
    .article-section h3 {
        font-size: 1.1rem !important;
    }
    .SeoSection ul li::before {
        top: 19px;
    }
}
