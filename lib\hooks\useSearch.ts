// import {getProducts} from "@/lib/services/productService";
import {useQuery} from "@tanstack/react-query";
import {useState} from "react";
import {ProductFilterOptions} from "@/lib/types/product.types";
import { getProducts } from "@/actions/product.action";

export const useSearch = () => {
    const [productParams, setProductParams] = useState<ProductFilterOptions>({
        page: 1,
        limit: 7,
        has_guarantee_only: undefined,
        in_stock_only: undefined,
        max_price: undefined,
        min_price: undefined,
        search: '',
        sort: undefined,
    });

    const paginatedQuery = useQuery({
        queryKey: ['products', productParams.search],
        queryFn: () => getProducts(productParams),
        staleTime: 5 * 60 * 1000,
        enabled: productParams.search!.length > 0,
    });

    return {
        paginatedQuery,
        productParams,
        setProductParams,
    };
};