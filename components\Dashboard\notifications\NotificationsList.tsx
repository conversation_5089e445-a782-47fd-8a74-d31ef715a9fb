import { getUserNotifications } from '@/actions/notifications.action'
import AlertIcon from '@/components/common/svg/AlertIcon'
import { ApiResponse } from '@/lib/types/favorites.types'
import { NotificationsData, NotificationsResponse } from '@/lib/types/notifications.types'
import React, { useEffect, useState } from 'react'

const NotificationsList = () => {
    const [notifications, setNotifications] = useState<NotificationsData>()
    useEffect(() => {
        const fetchNotifications = async () => {
            try {
                const notifications: ApiResponse<NotificationsData> = await getUserNotifications()
                console.log(notifications);
                setNotifications(notifications.data)
            } catch (error) {
                console.log(error);

            }
        }
        fetchNotifications()

    }, [])

    return (
        <div className="p-4 space-y-7 overflow-y-auto">
            {/* reply or accordion notif  */}
            {/* <div>
               
            </div> */}

            {
                notifications?.data.length ?
                    notifications.data.map((notification) => (
                        <div key={notification.id}>
                            <div className="flex justify-between items-center mb-5">
                                <div className="flex justify-start items-center gap-3">
                                    <div className="h-full bg-primary rounded-full p-1">
                                        <AlertIcon className="w-6 h-6 text-white" />
                                    </div>
                                    <div className="text-xs">
                                        <p className="mb-2"> {notification.title} </p>
                                        <p> {notification.created_at} </p>
                                    </div>
                                </div>
                                <div className="flex flex-col items-end gap-3">
                                    <p className="p-1 bg-primary w-1 rounded-full text-left"></p>
                                    <p className="text-xs"> {notification.created_date_ago} </p>
                                </div>
                            </div>
                            <div className="flex justify-end ">
                                <p className="text-xs w-[85%] bg-gray-200 p-1 px-3 rounded-xl text-justify leading-5">
                                    {notification.body}
                                </p>
                            </div>
                        </div>))
                    : <p>هیچ اعلانی وجود ندارد</p>

            }
            {/* <div>
                <div className="flex justify-between items-center mb-3">
                    <div className="flex justify-start items-center gap-3">
                        <div className="h-full bg-gray-400 rounded-full p-1">
                            <AlertIcon className="w-6 h-6 text-white" />
                        </div>
                        <div className="text-xs">
                            <p className="mb-2">عنوان پیام و اعلان شما در اینجا</p>
                            <p>شنبه 3:8 ب.ظ</p>
                        </div>
                    </div>
                    <div className="flex flex-col items-end gap-3">
                        <p className="p-1 bg-primary w-1 rounded-full text-left"></p>
                        <p className="text-xs">دو دقیقه پیش</p>
                    </div>
                </div>
            </div> */}
        </div>
    )
}

export default NotificationsList