import { GenericResponse, ProductFilterOptions, ProductResponse, ProductResponseData } from "@/lib/types/product.types";
// import {getProductsByCategory} from "@/lib/services/productService";
import { getCategoriesBySlug, getCategoryAttributes, getCategoryBreadCrumb, getProductsByCategory } from "@/actions/product.action";
import { headers } from "next/headers";
import ShopCategorySlugComponent from "@/components/shop/ShopCategorySlugComponent";
import CategoryService from "@/lib/services/category.service";
import { Category, CategorySearchableAttributes } from "@/lib/types/category.types";

type Props = {
    searchParams: Promise<ProductFilterOptions>
    params: Promise<{ categorySlug: string }>
}


export default async function ProductsByCategoryPage({ searchParams, params }: Props) {

    const filterParams = await searchParams
    const {
        in_stock_only = 'true',
        page,
        limit = 20
    } = filterParams

    const { categorySlug } = await params

    const userAgent = (await headers()).get('user-agent') || ''
    const isMobile = /mobile/i.test(userAgent)

    const productFilter: ProductFilterOptions = {
        ...filterParams,
        in_stock_only,
        page: page ? page : !isMobile ? 1 : undefined,
        limit,
    }

    // let productResponse: GenericResponse<ProductResponseData>;
    // let categoryBreadCrumbResponse: GenericResponse<Category[]>;
    // let categoriesResponse: GenericResponse<Category[]>
    // let categoryAttributesResponse: GenericResponse<Category[]>
    const productResponse = await getProductsByCategory(categorySlug, productFilter)
    const categoryBreadCrumbResponse : GenericResponse<Category[]> = await getCategoryBreadCrumb(categorySlug)
    const categoriesResponse : GenericResponse<Category[]> = await getCategoriesBySlug(categorySlug)
    const categoryAttributesResponse : GenericResponse<CategorySearchableAttributes[]> = await getCategoryAttributes(categorySlug)
    console.log(productResponse);
    // try {
    //     // [
    //     //     productResponse,
    //     //     categoryBreadCrumbResponse,
    //     //     categoriesResponse,
    //     //     categoryAttributesResponse
    //     // ]
    //     //     = await Promise.all([
    //     //         getProductsByCategory(categorySlug, productFilter),
    //     //         // CategoryService.getCategoryBreadCrumb(categorySlug),
    //     //         getCategoryBreadCrumb(categorySlug),
    //     //         // CategoryService.getCategoriesBySlug(categorySlug),
    //     //         getCategoriesBySlug(categorySlug),
    //     //         // CategoryService.getCategoryAttributes(categorySlug)
    //     //         getCategoryAttributes(categorySlug)
    //     //     ]);
    //     console.log("_________________");

    //     console.log(categoryAttributesResponse);
    //     console.log('product', productResponse);
    // } catch (error) {
    //     console.error("Error fetching data:", error);
    // }

    const categoriesForBreadCrumb = [{ slug: "", title: "فروشگاه" }, ...(categoryBreadCrumbResponse?.data || [])]
    const categories = [{ slug: "", title: "همه کالاها" }, ...(categoriesResponse?.data || [])]
    const colorFilterResults = (categoryAttributesResponse?.data) ? categoryAttributesResponse?.data.find((c) => c.english_title === 'color') : undefined
    const otherFilterResults = (categoryAttributesResponse?.data) ? categoryAttributesResponse?.data.filter((c) => c.english_title !== 'color') : undefined

    return (
        <>
            <ShopCategorySlugComponent
                key={categorySlug} // Force re-render when category changes
                params={productFilter}
                data={productResponse}
                isMobile={isMobile}
                categorySlug={categorySlug}
                categoriesForBreadCrumb={categoriesForBreadCrumb}
                categories={categories}
                colors={colorFilterResults}
                filters={otherFilterResults || []}
            />
        </>
    );
}
