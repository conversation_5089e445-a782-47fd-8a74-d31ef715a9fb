import { apiClient } from "@/lib/apiClient"
import { Metada<PERSON> } from "next"
import { CateoryResponse } from "@/lib/types/article.types"
import BlogMainContent from "@/components/blog/main/BlogMainContent"
import { getBlogPosts } from "@/actions/blog.action"

export const metadata: Metadata = {
  // robots: 'noindex, nofollow',
};


const BlogPage = async () => {
  const CategoriesResponse: CateoryResponse = await apiClient("categories")
    .then(res => res.json())

  // Use the new action function for consistency
  const articlesResponse = await getBlogPosts(1); // First page
  const ArticlesResponse = articlesResponse.data;

  console.log(ArticlesResponse);

  return (
    <BlogMainContent
      ArticlesResponse={ArticlesResponse.data || []}
      CategoriesResponse={CategoriesResponse}
      currentPage={ArticlesResponse.meta?.current_page || 1}
      lastPage={ArticlesResponse.meta?.last_page || 1}
    />
  )

}

export default BlogPage