import { apiClient } from "@/lib/apiClient"
import { Metadata } from "next"
import { CateoryResponse } from "@/lib/types/article.types"
import BlogMainContent from "@/components/blog/main/BlogMainContent"
import { getBlogPosts } from "@/actions/blog.action"
import { notFound } from "next/navigation"

export const metadata: Metadata = {
  // robots: 'noindex, nofollow',
};

type PageProps = {
  params: Promise<{ page: string }>
}

/**
 * Blog pagination page that handles:
 * - /blog/2, /blog/3, etc. (main blog pagination)
 */
const BlogPaginationPage = async ({ params }: PageProps) => {
  const { page: pageParam } = await params;
  
  // Parse page number
  const page = parseInt(pageParam);
  
  // If page is not a valid number or less than 2, redirect to main blog or 404
  if (isNaN(page) || page < 2) {
    notFound();
  }
  
  try {
    // Fetch categories and articles
    const CategoriesResponse: CateoryResponse = await apiClient("categories")
      .then(res => res.json());
    
    const articlesResponse = await getBlogPosts(page);
    
    if (!articlesResponse.success) {
      notFound();
    }
    
    const ArticlesResponse = articlesResponse.data;
    
    // Check if page exists
    if (!ArticlesResponse.data || ArticlesResponse.data.length === 0) {
      notFound();
    }
    
    return (
      <BlogMainContent 
        ArticlesResponse={ArticlesResponse.data || []} 
        CategoriesResponse={CategoriesResponse}
        currentPage={ArticlesResponse.meta?.current_page || page}
        lastPage={ArticlesResponse.meta?.last_page || 1}
      />
    );
    
  } catch (error) {
    console.error("Error fetching blog data:", error);
    notFound();
  }
}

export default BlogPaginationPage
