import BlogComments from '@/components/blog/SinglePage/BlogComments';
import ChildSchema from '@/components/common/ChildSchema';
import InquiryComponent from '@/components/inquiry/InquiryComponent';
import ArticleSection from '@/components/InquiryStaticComponents/ArticleSection';
import Faq from '@/components/InquiryStaticComponents/Faq';
import { PageContentResponse } from '@/lib/types/types';
import { getPageContent } from '@/lib/utils';
import React from 'react'

export async function generateMetadata() {
    const data = await getPageContent("motor-tax");

    return {
        title: data.meta_title,
        description: data.meta_description,
        keywords: data.tags || [],
        ...(data.meta_search && { robots: data.meta_search }),
        ...(data.canonical && { alternates: { canonical: data.canonical } }),
    };
}


const isMotor: boolean = true
const withDetails: boolean | undefined = true


const MotortaxPage = async () => {
    const status = process.env.NEXT_PUBLIC_MOTOR_TAX!
    const data: PageContentResponse = await getPageContent("motor-tax")
    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id="motor-tax"
                    schema={schema}
                />
            }
            <InquiryComponent status={status} title={title || ""} isMotor={isMotor} withDetails={withDetails} />
            {/* <CarTaxAboutService /> */}
            {
                description &&
                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
            <div className="md:max-w-7xl mx-auto mb-10 px-3">
                {/* <UserComments /> */}
                <BlogComments contentId={data.id} />
            </div>

        </>
    );
}

export default MotortaxPage