'use server'

import {ActionResult, PaymentArg, TransactionResponse, PaymentResponse} from "@/lib/types/action-types";
import {handleActionErrorResponse} from "@/utils/helpers";
import paymentService from "@/lib/services/payment.service";
import { apiClient } from "@/lib/apiClient";

export async function paymentPost(args: PaymentArg): Promise<ActionResult<PaymentResponse>> {
    try {
        const responseResult = await paymentService.paymentPost<PaymentResponse>({payload: args});
        return {
            success: true,
            data: responseResult.data,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function paymentVerificationPost(args: {
    Authority: string,
    Status: string
}): Promise<ActionResult<TransactionResponse>> {
    try {
        const responseResult = await paymentService.paymentVerificationPost<TransactionResponse>({payload: args});
        console.log("--------------------------", responseResult);
        
        return {
            success: true,
            data: responseResult.data,
            callbackUrl: responseResult.data.callbackUrl,
        }
    } catch (error: any) {
        console.log("+++++++++++++++++++++++++++++++++",error);
        
        return handleActionErrorResponse(error)
    }
}


export async function createInvoice(address_id: string, deliveryId: string ,discount_code?: string) {
    try {
        // const response = await fetch("https://shop-khodrox.liara.run/api/v1/invoices", {
        //     method: "POST",
        //     headers: {
        //         "Authorization": "matin_token",
        //         "X-Application-Token": "matin_token",
        //         "Content-Type": "application/json",
        //         "Accept": "application/json"
        //     },
        //     body: JSON.stringify({
        //         address_id,
        //         discount_code
        //     })
        // })
        // return response
        const response = await apiClient("invoices", {
            base: "alt",
            method: "POST",
            body: {
                address_id,
                delivery_method_id: deliveryId,
                discount_code
            },
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error creating invoice:", error);
        return { success: false, error };
    }
}
export async function invoicePayment(invoice_id: string, payment_method: string) {
    try {
        // const response = await fetch("https://shop-khodrox.liara.run/api/v1/invoices/pay", {
        //     method: "POST",
        //     headers: {
        //         "Authorization": "matin_token",
        //         "X-Application-Token": "matin_token",
        //         "Content-Type": "application/json",
        //         "Accept": "application/json"
        //     },
        //     body: JSON.stringify({
        //         invoice_id,
        //         payment_method
        //     })
        // })
        const response = await apiClient("invoices/pay", {
            base: "alt",
            method: "POST",
            body: {
                invoice_id,
                payment_method,
                price_unit_id: 1
            },
        })
        // return response
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error creating invoice:", error);
        return { success: false, error };
    }
}

export async function DiscountCode(code: string) {
    try {
        // const response = await fetch(`https://shop-khodrox.liara.run/api/v1/cart?discount_code=${code}`, {
        //     method: "GET",
        //     headers: {
        //         "Authorization": "matin_token",
        //         "Content-Type": "application/json",
        //         "X-Application-Token": "matin_token",
        //         "Accept": "application/json"
        //     }
        // })
        const response = await apiClient(`cart?discount_code=${code}`, {
            base: "alt",
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error creating invoice:", error);
        return { success: false, error };
    }
}