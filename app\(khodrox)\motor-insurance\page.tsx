import Faq from '@/components/InquiryStaticComponents/Faq';
// import envConfig from "@/lib/config-env";
import InsuranceComponent from "@/components/CarInsurance/InsuranceComponent";
import ChildSchema from '@/components/common/ChildSchema';
import { PageContentResponse } from '@/lib/types/types';
import ArticleSection from '@/components/InquiryStaticComponents/ArticleSection';
import { getPageContent } from '@/lib/utils';
import BlogComments from '@/components/blog/SinglePage/BlogComments';


export async function generateMetadata() {
    const data = await getPageContent("motor-insurance");
    
    return {
        title: data.meta_title,
        description: data.meta_description,
         keywords: data.tags || [],
    ...(data.meta_search && { robots: data.meta_search }),
    ...(data.canonical && { alternates: { canonical: data.canonical } }),
    };
}


const isMotor: boolean = true
// const env = envConfig()
const status = process.env.NEXT_PUBLIC_MOTOR_INSURANCE!
const page = async () => {
    const data: PageContentResponse = await getPageContent("motor-insurance")
    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id='motor-insurance'
                    schema={schema}
                />
            }
            <InsuranceComponent
                title={title || ""}
                isMotor={isMotor}
                status={status}
            />
            {/* <MotorInsuranceAbout /> */}
            {
                description &&
                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
            <div className="md:max-w-7xl mx-auto mb-10 px-3">
                  {/* <UserComments /> */}
                  <BlogComments contentId={data.id} />
            </div>
        </>
    )
}

export default page