import { NextRequest, NextResponse } from 'next/server';
import { getCarIdDocumentsInquiry } from '@/actions/inquiry.action';

export async function POST(request: NextRequest) {
    try {
        const data = await request.json();
        const response = await getCarIdDocumentsInquiry(data);
        return NextResponse.json(response);
    } catch (error) {
        console.error('API route error:', error);
        return NextResponse.json(
            { success: false, error: 'Failed to process request' },
            { status: 500 }
        );
    }
}
