type Props = {
    title: string,
    description: string,
}

export function PageDescription({ title, description }: Props) {
    return (
        <div className='px-2 md:px-10 text-center'>
            <h1
                style={{
                    color: '#212121',
                    textAlign: 'center',
                    // fontSize: '1.2rem', 
                    lineHeight: '1.5rem',
                }}
                className="md:text-2xl md:whitespace-nowrap max-md:leading-8"
            >
                {title}
            </h1>
            <p className='text-[#596068]  mt-4 md:mt-2 text-center font-light text-sm pr-1'>{description}</p>
        </div>
    );
}
