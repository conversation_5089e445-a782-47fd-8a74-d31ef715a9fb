"use client"

import ChatIcon from '@/components/common/svg/ChatIcon'
import ChatIconFilled from "@/components/common/svg/ChatIconFilled"
import ChatSmileIcon from '@/components/common/svg/ChatSmileIcon'
import MoreVerticalDots from '@/components/common/svg/MoreVerticalDots'
import { Ticket, TicketMessage, TicketMessagesResponse } from '@/lib/types/tickets.types'
import { ChevronsLeft, CornerUpLeft, Plus, Search, Send } from 'lucide-react'
import Image from 'next/image'
import ChatSmile from "@/public/assets/images/chat-smile.png"
import { useState, useEffect, useRef } from 'react'
import { getTicketMessages, sendTicketMessage } from '@/actions/tickets.action'
import toast from 'react-hot-toast'
import { ApiResponse } from '@/lib/types/favorites.types'
import Link from 'next/link'
import { useRouter } from 'nextjs-toploader/app'
import { useSearchParams } from 'next/navigation'




function translateTicketStatus(status: string) {
    switch (status) {
        case 'pending':
            return { title: 'در انتظار پاسخ', color: 'bg-yellow' };
        case 'answered':
            return { title: 'پاسخ داده شده', color: 'bg-[#2DC058]' };
        case 'closed':
            return { title: 'بسته شده', color: 'bg-[#9DA5B0]' };
        default:
            return { title: 'نامشخص', color: 'bg-gray-400' };
    }
}

const TicketsClient = ({ tickets }: { tickets: Ticket[] }) => {
    const searchParams = useSearchParams()
    const ticketId = searchParams.get('ticketId')
    const router = useRouter()
    const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(tickets.find(ticket => ticket.id.toString() === ticketId) || null);
    const [messages, setMessages] = useState<TicketMessage[]>([]);
    const [newMessage, setNewMessage] = useState('');
    const [isLoadingMessages, setIsLoadingMessages] = useState(false);
    const [isSendingMessage, setIsSendingMessage] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);

    // Fetch messages when a ticket is selected
    useEffect(() => {
        if (selectedTicket) {
            fetchTicketMessages(selectedTicket.id.toString());
        }
    }, [selectedTicket]);

    // Auto-scroll to bottom when messages change
    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);

    useEffect(() => {
        const id = searchParams.get('ticketId');
        if (id) {
            const ticket = tickets.find(t => t.id.toString() === id);
            setSelectedTicket(ticket || null);
        } else {
            setSelectedTicket(null); // Clear selected ticket
        }
    }, [searchParams, tickets]);


    const lastFetchedId = useRef<string | null>(null);

    const fetchTicketMessages = async (ticketId: string) => {
        if (lastFetchedId.current === ticketId) return; // جلوگیری از تکرار
        lastFetchedId.current = ticketId;

        setIsLoadingMessages(true);
        try {
            const response = await getTicketMessages(ticketId);
            if (response.success) {
                setMessages(response.data.tickets || []);
            } else {
                toast.error('خطا در بارگذاری پیام‌ها');
                setMessages([]);
            }
        } catch {
            toast.error('خطا در بارگذاری پیام‌ها');
            setMessages([]);
        } finally {
            setIsLoadingMessages(false);
        }
    };


    const handleSendMessage = async () => {
        if (!selectedTicket || !newMessage.trim()) return;

        setIsSendingMessage(true);
        try {
            debugger
            const response = await sendTicketMessage(
                selectedTicket.id,
                newMessage.trim(),
                selectedTicket.ticket_status
            );

            if (response.success) {
                toast.success('پیام با موفقیت ارسال شد');
                setNewMessage('');
                // Refresh messages
                await fetchTicketMessages(selectedTicket.id.toString());
            } else {
                toast.error(response.message || 'خطا در ارسال پیام');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            toast.error('خطا در ارسال پیام');
        } finally {
            setIsSendingMessage(false);
        }
    };

    const handleTicketClick = (ticket: Ticket) => {
        // setSelectedTicket(ticket);
        router.push(`?ticketId=${ticket.id}`)
        setMessages([]); // Clear previous messages
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    return (
        <>
            {/* <div className={`w-full rounded-2xl bg-white flex items-center px-4 h-20 ${selectedTicket ? 'max-md:hidden' : ''}`}> */}
            {/* <div className="flex items-center gap-3">
                    <FilterIcon />
                    <Link href="#" >تیکت های پشتیبانی</Link>
                    <Link href="#" >تیکت های خریدار</Link>
                </div> */}
            {/* <div className="flex items-center justify-between w-full">
                    <p>
                        ایجاد تیکت
                    </p>
                    <Link href={"/dashboard/create-ticket"} className="p-2.5  bg-yellow text-white rounded-full">
                        <Plus />
                    </Link>
                </div>
            </div> */}

            <div className="md:mt-8 flex md:justify-between md:h-[35rem] max-md:flex-col">
                <div className={`bg-white rounded-2xl p-4 md:w-[40%] ${selectedTicket ? 'max-md:hidden' : ''}`}>
                    <div className="flex justify-between px-5">
                        <div className="flex items-center gap-2">
                            <ChatSmileIcon />
                            <p>تیکت ها</p>
                            <span> ({tickets.length}) </span>
                        </div>
                        <div>
                            <Image src={ChatSmile} alt="chat-smile" />
                        </div>
                    </div>
                    {/* <div>
                        <div className="w-full relative px-5 mt-5">
                            <input
                                // ref={inputRef}
                                type="text"
                                // value={searchValue}
                                // onChange={handleChange}
                                // onKeyDown={handleKeyDown}
                                // onFocus={handleFocus}
                                disabled
                                placeholder="جستجو"
                                className="w-full py-3 bg-[#F9FAFB] pl-4 pr-10 text-gray-500  border rounded-xl outline-none focus:ring-2 focus:ring-gray-300 placeholder-gray-400 text-sm text-right"
                            />
                            <div
                                // onClick={handleLinkClick}
                                className="absolute left-10 top-1/2 transform -translate-y-1/2 cursor-pointer">
                                <Search className="w-5 h-5 text-gray-400" />
                            </div> */}

                    {/* {searchValue && (
                                <button
                                    className="absolute right-4 top-1/2 transform -translate-y-1/2"
                                >
                                    <X
                                        onClick={handleClear}
                                        className="w-5 h-5 text-gray-400 hover:text-gray-600" />
                                </button>
                            )} */}
                    {/* </div> */}
                    {/* </div> */}
                    <div className="flex items-center justify-center text-center w-full mt-5">
                        <Link href={"/dashboard/create-ticket"} className="p-2.5 w-full flex justify-center text-center bg-primary text-white rounded-xl">
                            <Plus /> ایجاد تیکت
                        </Link>
                    </div>
                    <div className="md:mt-3 md:overflow-y-auto md:h-[75%] ltr max-md:pb-16">
                        {
                            tickets.length ?
                                tickets.map((ticket, index) => (
                                    <div
                                        key={ticket.id}
                                        className={`mt-8 md:p-3 pt-5 ${index === 0 ? '' : 'border-t'} cursor-pointer transition-all duration-200 hover:bg-gray-50 ${selectedTicket?.id === ticket.id ? 'bg-blue-50 border-l-4 border-l-primary' : ''
                                            }`}
                                        onClick={() => handleTicketClick(ticket)}
                                    >
                                        <div className=" flex justify-between items-end">
                                            <div className="flex gap-3  items-center">
                                                <div className={`p-4 rounded-full ${selectedTicket?.id === ticket.id
                                                    ? 'bg-[#1F84FB] text-white'
                                                    : 'bg-gradient-to-l from-[#F0F0F0] to-transparent'
                                                    }`}>
                                                    {selectedTicket?.id === ticket.id ? (
                                                        <ChatIconFilled size={28} bgColor="#84b9f5" textColor="#ffffff" />
                                                    ) : (
                                                        <ChatIcon />
                                                    )}
                                                    {/* <ChatIcon /> */}
                                                </div>
                                                <div className="flex flex-col justify-between gap-2">
                                                    <h4 className="text-base"> {ticket.title} </h4>
                                                    <p className="flex gap-3 text-sm items-center">
                                                        <span className="text-gray-400">
                                                            {ticket.created_date_ago}
                                                        </span>
                                                        <span className={`text-white max-md:text-xs px-2.5 py-[2px] rounded-full ${translateTicketStatus(ticket.ticket_status).color}`}>
                                                            {translateTicketStatus(ticket.ticket_status).title}
                                                        </span>
                                                    </p>
                                                </div>
                                            </div>
                                            <div>
                                                <ChevronsLeft className="opacity-50" />
                                            </div>
                                        </div >
                                        {/* <ChatIconFilled size={28} bgColor="#1F84FB" textColor="#ffffff" /> */}
                                    </div >
                                ))
                                :
                                ""
                        }

                    </div >

                    {/* <div className="mt-3 p-3 pt-5 bg-[#F4F6F8] rounded-2xl">
                        <div className="px-4 flex justify-between items-end ">
                            <div className="flex gap-3 items-center">
                                <div className="p-4 bg-[#1f39fb] rounded-full">
                                    <ChatIconFilled />
                                </div>
                                <div className="flex flex-col justify-between gap-2">
                                    <h4 className="text-base text-primary">موضوع ارسالی کاربر اینجا قرار میگرد</h4>
                                    <p className="flex gap-3 text-sm items-center">
                                        <span className="text-gray-400">
                                            24 ساعت پیش
                                        </span>
                                        <span className="text-white  text-sm px-2.5 py-[2px] rounded-full"> منتظر پاسخ </span>
                                    </p>
                                </div>
                            </div>
                            <div>
                                <ChevronsLeft className="opacity-50" />
                            </div>
                        </div>
                    </div>
                    <div className="mt-3 p-3 pt-5 rounded-2xl">
                        <div className="px-4 flex justify-between items-end ">
                            <div className="flex gap-3 items-center">
                                <div className="p-4 bg-gradient-to-l from-[#F0F0F0] to-transparent rounded-full">
                                    <ChatIcon />
                                </div>
                                <div className="flex flex-col justify-between gap-2">
                                    <h4 className="text-base">موضوع ارسالی کاربر اینجا قرار میگرد</h4>
                                    <p className="flex gap-3 text-sm items-center">
                                        <span className="text-gray-400">
                                            24 ساعت پیش
                                        </span>
                                        <span className="text-white bg-primary text-sm px-2.5 py-[2px] rounded-full"> در حال بررسی </span>
                                    </p>
                                </div>
                            </div>
                            <div>
                                <ChevronsLeft className="opacity-50" />
                            </div>
                        </div>
                    </div>
                    <div className="mt-3 p-3 pt-5 rounded-2xl">
                        <div className="px-4 flex justify-between items-end ">
                            <div className="flex gap-3 items-center">
                                <div className="p-4 bg-gradient-to-l from-[#F0F0F0] to-transparent rounded-full">
                                    <ChatIcon />
                                </div>
                                <div className="flex flex-col justify-between gap-2">
                                    <h4 className="text-base">موضوع ارسالی کاربر اینجا قرار میگرد</h4>
                                    <p className="flex gap-3 text-sm items-center">
                                        <span className="text-gray-400">
                                            24 ساعت پیش
                                        </span>
                                        <span className="text-white bg-[#2DC058] text-sm px-2.5 py-[2px] rounded-full"> پاسخ داده شده </span>
                                    </p>
                                </div>
                            </div>
                            <div>
                                <ChevronsLeft className="opacity-50" />
                            </div>
                        </div>
                    </div>
                    <div className="mt-3 p-3 pt-5 rounded-2xl">
                        <div className="px-4 flex justify-between items-end ">
                            <div className="flex gap-3 items-center">
                                <div className="p-4 bg-gradient-to-l from-[#F0F0F0] to-transparent rounded-full">
                                    <ChatIcon />
                                </div>
                                <div className="flex flex-col justify-between gap-2">
                                    <h4 className="text-base">موضوع ارسالی کاربر اینجا قرار میگرد</h4>
                                    <p className="flex gap-3 text-sm items-center">
                                        <span className="text-gray-400">
                                            24 ساعت پیش
                                        </span>
                                        <span className="text-white bg-[#2DC058] text-sm px-2.5 py-[2px] rounded-full"> پاسخ داده شده </span>
                                    </p>
                                </div>
                            </div>
                            <div>
                                <ChevronsLeft className="opacity-50" />
                            </div>
                        </div>
                    </div> */}
                </div >
                <div className={`bg-white rounded-2xl p-4 md:w-[58%] flex flex-col max-md:h-[90vh] ${selectedTicket ? '' : 'max-md:hidden'}`}>
                    {selectedTicket ? (
                        <>
                            {/* Header */}
                            <div className="flex justify-between items-center border-b pb-4 max-md:relative ">

                                <div className="flex gap-3 items-center">
                                    <div className="p-4 bg-gradient-to-l from-[#F0F0F0] to-transparent rounded-full">
                                        <ChatIcon />
                                    </div>
                                    <div className="flex flex-col justify-between gap-2 max-md:gap-5">
                                        <h4 className="text-base">{selectedTicket.title}</h4>
                                        <div className="flex gap-3 text-sm items-center">
                                            <p className="text-gray-400">
                                                {selectedTicket.created_date_ago}
                                            </p>
                                            <p className="text-gray-500">
                                                شماره تیکت: #{selectedTicket.id}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div className="flex flex-col gap-3 items-end justify-end mt-auto ">
                                    <div className="flex md:hidden gap-3 items-center max-md:absolute max-md:top-0 max-md:left-1">
                                        <CornerUpLeft onClick={() => router.back()} className="opacity-50" />
                                    </div>
                                    {/* <MoreVerticalDots className="ml-2" width={18} /> */}
                                    <span className={`text-white text-sm md:px-2.5 max-md:px-2 max-md:py-2 max-md:whitespace-nowrap max-md:text-xs md:py-[2px] rounded-full ${translateTicketStatus(selectedTicket.ticket_status).color}`}>
                                        {translateTicketStatus(selectedTicket.ticket_status).title}
                                    </span>
                                </div>
                            </div>

                            {/* Messages Area */}
                            <div className="flex-1 overflow-y-auto md:max-h-[400px] py-4 ltr">
                                {isLoadingMessages ? (
                                    <div className="flex justify-center items-center h-32">
                                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                                    </div>
                                ) : messages.length > 0 ? (
                                    <div className="space-y-4">
                                        {messages.map((message) => (
                                            <div
                                                key={message.id}
                                                className={`flex ${message.is_admin == false ? 'justify-start' : 'justify-end'}`}
                                            >
                                                <div
                                                    className={`max-w-[70%] p-3 rounded-lg ${message.is_admin == false
                                                        ? 'bg-primary text-white rounded-br-none'
                                                        : 'bg-gray-100 text-gray-800 rounded-bl-none'
                                                        }`}
                                                >
                                                    <p className="text-sm">{message.message}</p>
                                                    <div className="flex justify-between items-center mt-2 text-xs opacity-70">
                                                        <span>{message.user_name || (message.is_admin == false ? 'شما' : 'پشتیبانی')}</span>
                                                        <span>{message.created_at}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                        <div ref={messagesEndRef} />
                                    </div>
                                ) : (
                                    <div className="flex justify-center items-center h-32 text-gray-500">
                                        هنوز پیامی ارسال نشده است
                                    </div>
                                )}
                            </div>

                            {/* Message Input */}
                            <div className="border-t pt-4 mt-4">
                                <div className="flex gap-3 relative">
                                    <div className="flex-1">
                                        <textarea
                                            value={newMessage}
                                            onChange={(e) => setNewMessage(e.target.value)}
                                            onKeyDown={handleKeyDown}
                                            placeholder="پیام خود را بنویسید..."
                                            className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none resize-none"
                                            rows={3}
                                            disabled={isSendingMessage || selectedTicket.ticket_status === 'closed'}
                                        />
                                    </div>
                                    <button
                                        onClick={handleSendMessage}
                                        disabled={!newMessage.trim() || isSendingMessage || selectedTicket.ticket_status === 'closed'}
                                        className="absolute left-2 bottom-4 bg-primary text-white px-4 py-2 rounded-xl hover:bg-primary/90 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2 h-fit self-end"
                                    >
                                        {isSendingMessage ? (
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                        ) : (
                                            <Send size={16} />
                                        )}
                                        <span className='max-md:hidden'>{isSendingMessage ? 'در حال ارسال...' : 'ارسال'}</span>
                                    </button>
                                </div>
                                {selectedTicket.ticket_status === 'closed' && (
                                    <p className="text-sm text-gray-500 mt-2">این تیکت بسته شده و امکان ارسال پیام وجود ندارد.</p>
                                )}
                            </div>
                        </>
                    ) : (
                        <div className="flex-1 flex justify-center items-center text-gray-500">
                            <div className="text-center">
                                <ChatIcon className="mx-auto mb-4 opacity-50" />
                                <p>برای مشاهده پیام‌ها، یک تیکت را انتخاب کنید</p>
                            </div>
                        </div>
                    )}
                </div>
            </div >
        </>
    )
}

export default TicketsClient