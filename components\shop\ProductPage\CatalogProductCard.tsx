"use client"
import { <PERSON><PERSON><PERSON>ist, ChartSpline, ScanLine, <PERSON>hare2, Bell<PERSON>ing, Heart, Loader2 } from "lucide-react"
// import ProductGallery from './ProductGallery'
import { Gallery } from "@/lib/types/product.types";
import { addToFavorites, deleteFromFavorites } from "@/actions/favorites.action";
import toast from "react-hot-toast";
import { useState } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import dynamic from "next/dynamic";
const ProductGallery = dynamic(() => import('./ProductGallery'), {
  loading: () => <div className=''></div>,
  ssr: false,
});


const CatalogProductCard = ({ gallery, productId, isFavorite, slug }: { gallery: Gallery[], productId: string, isFavorite: boolean, slug: string }) => {
    console.log(gallery);
    console.log("-----------id----------", productId);
    const [isAddingToFavorites, setIsAddingToFavorites] = useState(false)
    const [isAddedToFavorites, setIsAddedToFavorites] = useState(isFavorite || false)
    const pathname = usePathname();
        const params = useSearchParams()
        const product_id = params.get('product_id')
        console.log("pathname: ",pathname);
        console.log(product_id);

    const handleShare = async () => {
        const url = process.env.NEXT_PUBLIC_SHARE_BASE_URL + `${product_id}` 

        if (navigator.share) {
            try {
                await navigator.share({
                    title: document.title,
                    // text: "این محصول رو ببین!",
                    url,
                });
                toast.success("لینک با موفقیت به اشتراک گذاشته شد");
            } catch  {
                toast.error("اشتراک‌گذاری لغو شد");
            }
        } else {
            try {
                await navigator.clipboard.writeText(url);
                toast.success("لینک کپی شد!");
            } catch {
                toast.error("خطا در کپی کردن لینک");
            }
        }
    };
    const handleAddToFavorites = async () => {
        debugger
        setIsAddingToFavorites(true)
        if (!isAddedToFavorites) {
            const addToFavoritesResponse = await addToFavorites(+productId)
            if (addToFavoritesResponse.success) {
                toast.success("محصول به لیست علاقه مندی ها اضافه شد")
                setIsAddedToFavorites(true)
            } else {
                toast.error("خطایی رخ داده")
            }
        } else {
            const deleteFromFavoritesResponse = await deleteFromFavorites(slug)
            if (deleteFromFavoritesResponse.success) {
                toast.success("محصول از لیست علاقه مندی ها حذف شد")
                setIsAddedToFavorites(false)
            } else {
                toast.error("خطایی رخ داده")
            }
        }
        setIsAddingToFavorites(false)
    }

    return (
        <div className='flex flex-col md:w-[30%] rounded-3xl'>
            <div className='max-md:hidden'>
                <ul className='flex product-options justify-around mb-1 '>
                    <li>
                        <LayoutList />
                    </li>
                    <li>
                        <ChartSpline />
                    </li>
                    <li>
                        <ScanLine />
                    </li>
                    <li onClick={handleShare}>
                        <Share2 />
                    </li>
                    <li>
                        <BellRing />
                    </li>
                    {
                        <li
                            onClick={isAddingToFavorites ? undefined : handleAddToFavorites}
                            className={`cursor-pointer rounded-full p-1 transition-colors duration-300  ${isAddedToFavorites ? '!bg-red-500 text-white' : 'hover:bg-gray-100'
                                }`}
                        >
                            {isAddingToFavorites ? (
                                <Loader2 className="animate-spin" />
                            ) : (
                                <Heart className="" />
                            )}
                        </li>
                    }


                </ul>
            </div>
            <div className="max-md:w-full">
                <ProductGallery images={gallery} />

            </div>
        </div>
    )
}

export default CatalogProductCard