'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

/**
 * Dynamic notification component loader
 * This component handles the dynamic loading of the notification component to reduce bundle size
 */
const NotificationComponent = dynamic(
  () => import('./SendNotification'),
  {
    ssr: false,
    loading: () => null, // No loading indicator needed for notifications
  }
);

/**
 * DynamicNotificationLoader component
 * This component conditionally loads the notification component only when needed
 * It checks browser support and user preferences before loading Firebase dependencies
 */
export default function DynamicNotificationLoader() {
  const [shouldLoadNotification, setShouldLoadNotification] = useState(false);

  useEffect(() => {
    // Function to check if we should load the notification component
    const checkShouldLoadNotification = async () => {
      // Check if we're in browser environment
      if (typeof window === 'undefined') {
        return false;
      }

      // Dynamically import utility functions to check browser support
      try {
        const {
          areNotificationRequirementsMet,
          getNotificationPermission
        } = await import('@/lib/notification/notificationUtils');

        // Check if all requirements are met (notifications, service workers, secure context)
        if (!areNotificationRequirementsMet()) {
          return false;
        }

        // If permission is already denied, don't load
        if (getNotificationPermission() === 'denied') {
          return false;
        }

        // Check if user has already made a recent decision
        const NOTIFICATION_PREFERENCE_KEY = 'khodrox_notification_preference';
        const preferenceStr = localStorage.getItem(NOTIFICATION_PREFERENCE_KEY);

        if (preferenceStr) {
          try {
            const preference = JSON.parse(preferenceStr);

            // If user rejected recently (within 2 days), don't load
            if (preference.status === 'rejected') {
              const rejectionTime = preference.timestamp;
              const currentTime = Date.now();
              const twoDaysInMs = 2 * 24 * 60 * 60 * 1000;

              if (currentTime - rejectionTime < twoDaysInMs) {
                return false;
              }
            }
          } catch {
            // If there's an error parsing, continue to load
          }
        }

        return true;
      } catch (error) {
        console.error('Error checking notification requirements:', error);
        return false;
      }
    };

    // Check if we should load the notification component
    checkShouldLoadNotification().then(shouldLoad => {
      if (shouldLoad) {
        // Add a small delay to avoid blocking initial page load
        const timeoutId = setTimeout(() => {
          setShouldLoadNotification(true);
        }, 1000);

        return () => clearTimeout(timeoutId);
      }
    });
  }, []);

  // Only render the notification component if we should load it
  if (!shouldLoadNotification) {
    return null;
  }

  return <NotificationComponent />;
}
