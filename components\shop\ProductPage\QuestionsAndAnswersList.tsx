import BoxDone from "@/components/common/svg/BoxDone";
import React from "react";
import AccordionWrapper from "./AccordionWrapper";
import QuestionInput from "./QuestionInput";
import QuestionItem from "./QuestionItem";
import { Question } from "@/lib/types/product.types";

const QuestionsAndAnswersList = ({ questions }: { questions: Question[] }) => {
  return (
    <section
      className="bg-white mt-10 p-3 rounded-3xl scroll-mt-28"
      id="productQuestionsAndAnswers"
    >
      <AccordionWrapper title="پرسش و پاسخ" icon={<BoxDone />}>
        <div className="max-w-3xl mx-auto">
          <p className="mb-5 px-1">سوالی در مورد محصول دارید؟ کافیه بپرسید</p>

          <QuestionInput />
        </div>
        <div className="mt-8">
          <h3>{questions.length} سوال</h3>
          {questions.length &&
            questions.map((question) => (
              <QuestionItem key={question.id} question={question} />
            ))}
        </div>
      </AccordionWrapper>
    </section>
  );
};

export default QuestionsAndAnswersList;
