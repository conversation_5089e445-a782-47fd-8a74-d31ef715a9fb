import type { FirebaseApp } from 'firebase/app';
import type { Messaging } from 'firebase/messaging';

/**
 * Interface for Firebase message payload
 */
export interface FirebaseMessagePayload {
    notification?: {
        title?: string;
        body?: string;
        image?: string;
    };
    data?: Record<string, string>;
    from: string;
    collapseKey?: string;
    messageId: string;
}

/**
 * Interface for Firebase configuration
 */
export interface FirebaseConfig {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
    measurementId?: string;
}

/**
 * Interface for the notification service
 */
export interface NotificationService {
    init: () => Promise<void>;
    getToken: () => Promise<string>;
    onForegroundMessage: (callback: (payload: FirebaseMessagePayload) => void) => void;
    destroy: () => void;
}

/**
 * Creates a Firebase notification service using function-based approach
 * This is more readable and friendly than class-based approach
 * @param config - Firebase configuration object
 * @param vapidKey - VAPID key for FCM
 * @returns NotificationService - Service object with methods
 */
export function createNotificationService(
    config: FirebaseConfig, 
    vapidKey: string
): NotificationService {
    // Internal state - using closure instead of class properties
    let messaging: Messaging | null = null;
    let app: FirebaseApp | null = null;

    /**
     * Initialize Firebase app and messaging with dynamic imports
     * This method loads Firebase dependencies only when called
     */
    const init = async (): Promise<void> => {
        try {
            // Dynamically import Firebase dependencies
            const [
                { initializeApp },
                { getMessaging }
            ] = await Promise.all([
                import('firebase/app'),
                import('firebase/messaging')
            ]);

            // Initialize Firebase app
            app = initializeApp(config);
            
            // Initialize messaging
            messaging = getMessaging(app);
            
            // Request notification permission
            await Notification.requestPermission();
        } catch (error) {
            console.error('Failed to initialize Firebase messaging:', error);
            throw error;
        }
    };

    /**
     * Get FCM token with dynamic import
     * @returns Promise<string> - The FCM token
     */
    const getToken = async (): Promise<string> => {
        if (!messaging) {
            throw new Error('Messaging not initialized. Call init() first.');
        }

        try {
            // Dynamically import getToken function
            const { getToken: getTokenFn } = await import('firebase/messaging');
            
            return await getTokenFn(messaging, { 
                vapidKey: vapidKey 
            });
        } catch (error) {
            console.error('Failed to get FCM token:', error);
            throw error;
        }
    };

    /**
     * Set up foreground message listener with dynamic import
     * @param callback - Callback function to handle incoming messages
     */
    const onForegroundMessage = (callback: (payload: FirebaseMessagePayload) => void): void => {
        if (!messaging) {
            throw new Error('Messaging not initialized. Call init() first.');
        }

        // Dynamically import onMessage function
        import('firebase/messaging')
            .then(({ onMessage }) => {
                if (messaging) {
                    onMessage(messaging, callback);
                }
            })
            .catch(error => {
                console.error('Failed to set up foreground message listener:', error);
            });
    };

    /**
     * Clean up resources
     */
    const destroy = (): void => {
        messaging = null;
        app = null;
    };

    // Return the service object with all methods
    return {
        init,
        getToken,
        onForegroundMessage,
        destroy
    };
}

/**
 * Utility function to check if notifications are supported
 * @returns boolean - True if notifications are supported
 */
export function isNotificationSupported(): boolean {
    return typeof window !== 'undefined' && 'Notification' in window;
}

/**
 * Utility function to check if service workers are supported
 * @returns boolean - True if service workers are supported
 */
export function isServiceWorkerSupported(): boolean {
    return typeof window !== 'undefined' && 'serviceWorker' in navigator;
}

/**
 * Utility function to check if the context is secure (HTTPS)
 * @returns boolean - True if the context is secure
 */
export function isSecureContext(): boolean {
    return typeof window !== 'undefined' && window.isSecureContext;
}

/**
 * Utility function to check if all requirements for notifications are met
 * @returns boolean - True if all requirements are met
 */
export function areNotificationRequirementsMet(): boolean {
    return isNotificationSupported() && isServiceWorkerSupported() && isSecureContext();
}

/**
 * Utility function to get current notification permission status
 * @returns NotificationPermission - Current permission status
 */
export function getNotificationPermission(): NotificationPermission | 'unknown' {
    if (!isNotificationSupported()) {
        return 'unknown';
    }
    return Notification.permission;
}

/**
 * Utility function to request notification permission
 * @returns Promise<NotificationPermission> - The permission result
 */
export async function requestNotificationPermission(): Promise<NotificationPermission> {
    if (!isNotificationSupported()) {
        throw new Error('Notifications are not supported in this browser');
    }
    
    return await Notification.requestPermission();
}
