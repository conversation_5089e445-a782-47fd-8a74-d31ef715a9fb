"use server"

import { apiClient } from "@/lib/apiClient";
import { ApiResponse } from "@/lib/types/favorites.types";
import { NotificationsResponse } from "@/lib/types/notifications.types";



export async function getUserNotifications(page?: number, limit?: number, read?: string, type?: string) {
    try {
        const response = await apiClient(`user/notifications${page ? `?page=${page}` : ''}${limit ? `&limit=${limit}` : ''}${read ? `&read=${read}` : ''}${type ? `&type=${type}` : ''}`, {
            method: "GET",
            base: "alt",
        })
        console.log(response);
        
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting notifications:", error);
        return { success: false, error: "Failed to get notifications" };
    }
}
