"use client"

import { getCarIdDocumentsByTrace } from "@/actions/inquiry.action"
import { CarDocumentsData } from "@/lib/types/qabzino-types"
import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"

const CarDocumentsResult = () => {
    const [inquiryData, setInquiryData] = useState<CarDocumentsData | null>(null)
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null)
    const [mounted, setMounted] = useState(false)
    const params = useSearchParams()

    // Fix hydration issue by ensuring component is mounted
    useEffect(() => {
        setMounted(true)
    }, [])

    useEffect(() => {
        if (!mounted) return

        const traceNumber = params.get('trace_number') || '';

        const fetchData = async () => {
            if (traceNumber && !inquiryData) {
                const response = await getCarIdDocumentsByTrace(traceNumber);

                if (response?.success && response?.data?.result) {
                    setInquiryData(response.data);
                } else {
                    setError(response?.data?.message || response?.message || "خطایی رخ داده است");
                }
                setLoading(false);
            }
        };

        fetchData();
    }, [mounted, params, inquiryData]);

    const renderRow = (label: string, value?: string | number | boolean) => (
        <div className="flex justify-between border-b pb-6 border-gray-200">
            <span className="text-gray-500 text-base">{label}</span>
            <span className="font-medium">{value ?? '---'}</span>
        </div>
    );

    // Prevent hydration mismatch by not rendering until mounted
    if (!mounted || loading) {
        return (
            <div className="flex flex-col gap-6 animate-pulse">
                {[...Array(4)].map((_, i) => (
                    <div key={i} className="flex justify-between border-b pb-6 border-gray-200">
                        <div className="bg-gray-200 h-4 w-32 rounded" />
                        <div className="bg-gray-200 h-4 w-16 rounded" />
                    </div>
                ))}
            </div>
        );
    }

    return (
        <>
            {error ? 
                <h2 className="text-red-500 text-lg text-center py-5">{error}</h2>
                :
                <div className="flex flex-col gap-6">
                    {renderRow("تاریخ کارت", inquiryData?.result?.CardDateTime)}
                    {renderRow("بارکد پستی کارت", inquiryData?.result?.CardPostalBarcode)}
                    {renderRow("عنوان کارت", inquiryData?.result?.CardTitle)}
                    {renderRow("نوع کارت", inquiryData?.result?.CardType)}
                    {renderRow("تاریخ سند", inquiryData?.result?.DocumentDateTime)}
                    {renderRow("عنوان سند", inquiryData?.result?.DocumentTitle)}
                    {renderRow("نوع سند", inquiryData?.result?.DocumentType)}
                    {renderRow("کارت هوشمند", inquiryData?.result?.IsCardSmart ? "بله" : "خیر")}
                    {renderRow("پلاک", inquiryData?.result?.Plaque)}
                </div>
            }
        </>
    );
};

export default CarDocumentsResult;