"use client"

import { getCarIdDocumentsByTrace } from "@/actions/inquiry.action"
import { CarDocumentsData } from "@/lib/types/qabzino-types"
import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import CarDocumentDetails from "./CarDocumentDetails"
// import DisplayPlaque from "../common/DisplayPlaque"

const CarDocumentsResult = () => {
    const [inquiryData, setInquiryData] = useState<CarDocumentsData | null>(null)
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null)
    const params = useSearchParams()

    useEffect(() => {
        const traceNumber = params.get('trace_number') || '';
        const fetchData = async () => {
            debugger
            if (traceNumber && !inquiryData) {
                try {
                    const response = await getCarIdDocumentsByTrace(traceNumber);
                    if (response?.success && response?.data?.result) {
                        setInquiryData(response.data);
                    } else {
                        setError(response?.data?.message || "خطایی رخ داده است");
                    }
                    setLoading(false);
                    
                } catch (error) {
                    console.log(error);
                    
                    setError("خطایی رخ داده است");
                    setLoading(false);
                }
            }
        };

        fetchData();
    }, []);

    const renderRow = (label: string, value?: string | number | boolean) => (
        <div className="flex justify-between border-b pb-6 border-gray-200">
            <span className="text-gray-500 text-base">{label}</span>
            <span className="font-medium">{value ?? '---'}</span>
        </div>
    );

    if (loading) {
        return (
            <div className="flex flex-col gap-6 animate-pulse">
                {[...Array(7)].map((_, i) => (
                    <div key={i} className="flex justify-between border-b pb-6 border-gray-200">
                        <div className="bg-gray-200 h-4 w-32 rounded" />
                        <div className="bg-gray-200 h-4 w-16 rounded" />
                    </div>
                ))}
            </div>
        );
    }

    return (
        <>
            {error ? 
                <h2 className="text-red-500 text-lg text-center py-5">{error}</h2>
                :
                <div className="flex flex-col gap-6">
                    <div>
                        {/* <DisplayPlaque left={left || ""} right={right || ""} middle={mid || ""} alphabet={alphabet || ""}
                             isMotor={isMotor}/> */}
                    </div>
                    {/* {renderRow("تاریخ چاپ کارت خودرو", inquiryData?.result?.CardDateTime)}
                    {renderRow("تاریخ صدور کارت خودرو", inquiryData?.result?.CardIssuanceDateTime)}
                    {renderRow("بارکد پستی کارت خودرو", inquiryData?.result?.CardPostalBarcode)}
                    {renderRow("وضعیت کارت خودرو", inquiryData?.result?.CardTitle)}
                    {renderRow("نوع کارت خودرو", inquiryData?.result?.CardType)}
                    {renderRow("تاریخ چاپ سند(برگ سبز)", inquiryData?.result?.DocumentDateTime)}
                    {renderRow("تاریخ صدور سند(برگ سبز)", inquiryData?.result?.DocumentIssuanceDateTime)}
                    {renderRow("بارکد پستی برگه سبز خودرو)", inquiryData?.result?.DocumentPostalBarcode)}
                    {renderRow("وضعیت سند(برگ سبز)", inquiryData?.result?.DocumentTitle)}
                    {renderRow("نوع سند(برگ سبز)", inquiryData?.result?.DocumentType)}
                    {renderRow("وضعیت هوشمند بودن کارت", inquiryData?.result?.IsCardSmart ? "بله" : "خیر")} */}
                    {/* {renderRow("پلاک", inquiryData?.result?.Plaque)} */}
                    {
                        inquiryData?.result && (
                            <CarDocumentDetails detail={inquiryData?.result} />
                        )
                    }
                    {/* <CarDocumentDetails detail={inquiryData?.result} /> */}
                </div>
            }
        </>
    );
};

export default CarDocumentsResult;