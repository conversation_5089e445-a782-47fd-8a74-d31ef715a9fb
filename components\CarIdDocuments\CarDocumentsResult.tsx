"use client"

import { getCarIdDocumentsByTrace } from "@/actions/inquiry.action"
import { DrivingLicensePointData } from "@/lib/types/qabzino-types"
import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"

const CarDocumentsResult = () => {
    const [inquiryData, setInquiryData] = useState(null)
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null)
    const params = useSearchParams()
    const traceNumber = params.get('trace_number') || '';

    useEffect(() => {
        // در کامپوننت CarDocumentsResult، قبل از useEffect این را اضافه کنید:
console.log("Trace number from URL:", traceNumber);
console.log("Full params:", params.toString());
        const fetchData = async () => {
            if (!traceNumber) {
                setError("شناسه رهگیری یافت نشد");
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                const response = await getCarIdDocumentsByTrace(traceNumber);
                
                if (response?.success && response?.data?.result) {
                    setInquiryData(response.data);
                } else {
                    setError(response?.data?.message || response?.message || "خطایی رخ داده است");
                }
            } catch (err) {
                setError("خطا در دریافت اطلاعات");
                console.error("Error fetching data:", err);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []); 

    const renderRow = (label: string, value?: string | number | boolean) => (
        <div className="flex justify-between border-b pb-6 border-gray-200">
            <span className="text-gray-500 text-base">{label}</span>
            <span className="font-medium">{value ?? '---'}</span>
        </div>
    );

    if (loading) {
        return (
            <div className="flex flex-col gap-6 animate-pulse">
                {[...Array(4)].map((_, i) => (
                    <div key={i} className="flex justify-between border-b pb-6 border-gray-200">
                        <div className="bg-gray-200 h-4 w-32 rounded" />
                        <div className="bg-gray-200 h-4 w-16 rounded" />
                    </div>
                ))}
            </div>
        );
    }

    return (
        <>
            {error ? 
                <h2 className="text-red-500 text-lg text-center py-5">{error}</h2>
                :
                <div className="flex flex-col gap-6">
                    {renderRow("شماره گواهینامه", inquiryData?.CardDateTime)}
                    {renderRow("نمره منفی", inquiryData?.result?.CardPostalBarcode)}
                    {renderRow("اجازه رانندگی", inquiryData?.result?.CardTitle ? "دارد" : "ندارد")}
                    
                </div>                        
            }
        </>
    );
};

export default CarDocumentsResult;