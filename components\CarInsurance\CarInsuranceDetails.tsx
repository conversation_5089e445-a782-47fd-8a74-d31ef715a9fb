import React, { useState } from 'react'
import ChoiceWrapper from '../common/ChoiceWrapper';
import SubtractBorderIcon from '../common/svg/SubtractBorderIcon';
import SubtractIcon from '../common/svg/SubtractIcon';
import { cn } from '@/lib/utils';
import ResultDetailsUpIcon from '../common/svg/ResultDetailsUpIcon';
import { getViolationImageAction } from '@/actions/inquiry.action';
import { CameraIcon, IdCard, TextSelect } from 'lucide-react';
import toast from 'react-hot-toast';
import DialogModal from '../common/DialogModal';
import ResultDetailsLeftIcon from '../common/svg/ResultDetailsLeftIcon';
import CustomButton from '../UI/CustomButton';
import { InsuranceResult, LicenseStatusRecord } from '@/lib/types/qabzino-types';

interface Props {
    detail: InsuranceResult
    isCollapse?: boolean;
}


const CarInsuranceDetails = ({ detail, isCollapse = false }: Props) => {
   const [loading, setLoading] = useState(false);
      const [collapse, setCollapse] = useState(isCollapse)
  
      return (
          <>
              {loading && (
                  <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
                      <div className="w-10 h-10 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
                  </div>
              )}
  
              <ChoiceWrapper
                  backgroundColor='#F9FAFB'
                  borderColor={collapse ? "#EEEEEE" : "#CCCACA"}
                  className={collapse ? 'border-solid !border' : ''}
              >
                  <div
                      onClick={() => setCollapse(!collapse)}
                      className='relative  w-full cursor-pointer'>
                      <div className={cn('w-full p-2 pt-5 pb-10 max-h-[460px] duration-200 transition-all relative', {
                          "max-h-[215px]": collapse
                      })}>
                          <div className='absolute bottom-[-7px] left-[-13px]'>
                              {collapse ? <SubtractBorderIcon width={100} height={100} /> :
                                  <SubtractIcon width={100} height={100} />}
                          </div>
                          <div className='absolute bottom-[-13px] left-[22px] cursor-pointer'
                              onClick={() => setCollapse((prev) => !prev)}>
                              {
                                  !collapse ? (<ResultDetailsUpIcon width={30} height={30} />) : (<ResultDetailsLeftIcon />)
                              }
                          </div>
                          <div className='w-full flex justify-between items-center gap-x-1'>
                              <span>
                                  <p className='text-[#000000]'>{detail.FullName} </p>
                              </span>
                              <TextSelect color='#9DA5B0' height={40} width={40} />
                          </div>
                          {collapse && (
                              <div className='my-3 space-y-3'>
  
                                  <div className='w-full flex justify-between items-center text-[#596068]'>
                                      <span className='text-xs'>نام شرکت بیمه:</span>
                                      <span className='text-xs'>{detail.CompanyName}</span>
                                  </div>
                                  <div className='w-full flex justify-between items-center text-[#596068]'>
                                      <span className='text-xs'>شماره بیمه نامه:</span>
                                      <span className='text-xs'>{detail.InsuranceNumber}</span>
                                  </div>
                                  
                                  <div className='w-full flex justify-between items-center text-[#596068]'>
                                      <span className='text-xs'>شماره بیمه گذاری:</span>
                                      <span className='text-xs'>{detail.UniqueInsuranceCode}</span>
                                  </div>
                                  <div className='w-full flex justify-between items-center text-[#596068]'>
                                      <span className='text-xs'>کد ملی:</span>
                                      <span className='text-xs'>{detail.NationalID}</span>
                                  </div>
                                 
                              </div>
  
                          )}
                          <div className={cn('mt-5 flex flex-col items-center gap-5', {
                              "hidden": collapse,
                          })}>
                              <div className='w-full flex justify-between items-center text-[#596068] text-xs'>
                                  <span>نام شرکت بیمه:</span>
                                  <span>{detail.CompanyName}</span>
                              </div>
                              {/*<div className='w-full flex justify-between items-center text-[#596068]'>*/}
                              {/*    <span className='text-xs'>روش ثبت:</span>*/}
                              {/*    <span className='text-xs'>{detail.}</span>*/}
                              {/*</div>*/}
                              <div className='w-full flex justify-between items-center text-[#596068]'>
                                  <span className='text-xs'>کد ملی:</span>
                                  <span className='text-xs'>{detail.NationalID}</span>
                              </div>
                              <div className='w-full flex justify-between items-center text-[#596068]'>
                                  <span className='text-xs'>شماره بیمه نامه:</span>
                                  <span className='text-xs'>{detail.InsuranceNumber}</span>
                              </div>
                              <div className='w-full flex justify-between items-center text-[#596068]'>
                                  <span className='text-xs'>شماره بیمه گذاری:</span>
                                  <span className='text-xs'>{detail.UniqueInsuranceCode}</span>
                              </div>
                              <div className='w-full flex justify-between items-center text-[#596068]'>
                                  <span className='text-xs'>نوع بیمه:</span>
                                  <span className='text-xs'>{detail.InsuranceType}</span>
                              </div>
                              <div className='w-full flex justify-between items-center text-[#596068]'>
                                  <span className='text-xs'>تاریخ شروع بیمه:</span>
                                  <span className='text-xs'>{detail.StartDate}</span>
                              </div>
                              <div className='w-full flex justify-between items-center text-[#596068]'>
                                  <span className='text-xs'>تاریخ پایان بیمه:</span>
                                  <span className='text-xs'>{detail.EndDate}</span>
                              </div>
                              <div className='w-full flex justify-between items-center text-[#596068]'>
                                  <span className='text-xs'>تاریخ صدور بیمه:</span>
                                  <span className='text-xs'>{detail.IssueDate}</span>
                              </div>
                              
                          </div>
  
                      </div>
  
                  </div>
              </ChoiceWrapper>
  
  
          </>
      );
}

export default CarInsuranceDetails