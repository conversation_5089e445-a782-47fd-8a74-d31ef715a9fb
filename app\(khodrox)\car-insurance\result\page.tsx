import Card from '@/components/common/Card'
import LicensePointResult from '@/components/DrivingLicensePoint/LicensePointResult'
import Link from 'next/link'
import Container from '@/components/common/Container'
import React from 'react'

const CarInsuranceResultPage = () => {
  return (
     <Container>
            <Card className="!px-5 !pt-5 !pb-10 mt-5  min-h-96 w-full max-w-lg mb-20 md:mb-32 relative z-20">
                <h2 className="text-[#212121] text-base md:text-lg font-semibold mb-4 py-3">
                    نتیجه استعلام نمره منفی شما
                </h2>

                <LicensePointResult />
                <ul className="p-4 my-6 rounded-xl bg-orange-50 border border-yellow-400 border-dashed space-y-4 text-sm leading-6 text-gray-700">
                    <li className="flex flex-col items-start gap-1">
                        <span className="font-semibold text-gray-800">نمره منفی:</span>
                        عدد نمره منفی نشان‌دهنده میزان تخلفات حادثه‌ساز ثبت‌شده برای گواهینامه شماست. عدد صفر یعنی تاکنون هیچ نمره منفی برای شما ثبت نشده.
                    </li>
                    <li className="flex flex-col items-start gap-1">
                        <span className="font-semibold text-gray-800">اجازه رانندگی:</span>
                        اگر مقدار «دارد» نمایش داده شود، شما از نظر قانونی مجاز به رانندگی هستید. مقدار «ندارد» به‌معنای ممنوعیت رانندگی به دلایلی مانند نمره منفی بالا یا محدودیت قانونی است.
                    </li>
                    <li className="flex flex-col items-start gap-1">
                        <span className="font-semibold text-gray-800">شناسه قانون:</span>
                        این مقدار شناسه قانونی تخلف ثبت‌شده را نشان می‌دهد. اگر مقدار صفر باشد، یعنی تخلفی ثبت نشده یا شناسه مشخصی برای آن موجود نیست.
                    </li>
                </ul>


                <Link href={"/car-insurance"} className=" w-full mt-12 rounded-lg text-white mx-auto bg-primary hover:bg-blue-500 transition-all p-4 px-8 flex items-center justify-center">
                    <span className="text-base">استعلام جدید</span>
                </Link>
            </Card>

        </Container>
  )
}

export default CarInsuranceResultPage